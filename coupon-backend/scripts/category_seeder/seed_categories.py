
import psycopg2
import re
import time

# Database connection details (replace with your actual credentials)
DB_HOST = "**************"
DB_PORT = "14614"
DB_NAME = "coupons-go-org"
DB_USER = "user_SgXD8fYdcn7mPs6kttjk"
DB_PASSWORD = "password_BkGnEmjRrBEJYcv8xHsU"

# Category data with Lucide icons and appropriate colors
CATEGORIES = {
    "Health & Beauty": {"id": 1, "icon": "Heart"},
    "Pets & Animals": {"id": 2, "icon": "Dog"},
    "Travel & Transportation": {"id": 3, "icon": "Plane"},
    "Auctions & Classifieds": {"id": 4, "icon": "Gavel"},
    "Consumer Goods": {"id": 5, "icon": "ShoppingBag"},
    "Toys & Games": {"id": 6, "icon": "ToyBrick"},
    "Business & Industrial": {"id": 7, "icon": "Briefcase"},
    "Sports & Fitness": {"id": 8, "icon": "Dumbbell"},
    "Hobbies & Leisure": {"id": 9, "icon": "Paintbrush"},
    "People & Society": {"id": 10, "icon": "Users"},
    "Science & Reference": {"id": 11, "icon": "FlaskConical"},
    "Gifts & Events": {"id": 12, "icon": "Gift"},
    "Eco-Friendly Products": {"id": 13, "icon": "Leaf"},
    "Arts & Entertainment": {"id": 14, "icon": "Ticket"},
    "Food & Drink": {"id": 15, "icon": "Utensils"},
    "Jobs & Education": {"id": 16, "icon": "Book"},
    "Fashion & Apparel": {"id": 17, "icon": "Shirt"},
    "Home & Electronics": {"id": 18, "icon": "Home"},
    "Others": {"id": 19, "icon": "MoreHorizontal"},
}

def generate_slug(name):
    """Generates a slug from a category name."""
    s = name.lower()
    s = re.sub(r'[^\w\s-]', '', s)
    s = re.sub(r'[\s_]+', '-', s)
    s = re.sub(r'^-+|-+$', '', s)
    return s

def seed_categories():
    """Connects to the database and inserts the categories."""
    conn = None
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD,
        )
        cur = conn.cursor()

        for name, data in CATEGORIES.items():
            id = data["id"]
            icon = data["icon"]
            slug = generate_slug(name)
            created_at = updated_at = time.strftime('%Y-%m-%d %H:%M:%S')

            # Using ON CONFLICT to avoid errors if the category already exists
            cur.execute(
                """
                INSERT INTO categories (id, slug, name, icon, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO UPDATE SET
                    slug = EXCLUDED.slug,
                    name = EXCLUDED.name,
                    icon = EXCLUDED.icon,
                    updated_at = EXCLUDED.updated_at;
                """,
                (id, slug, name, icon, created_at, updated_at),
            )
            print(f"Inserted or updated category: {name}")

        conn.commit()
        cur.close()
        print("Categories seeded successfully!")

    except (Exception, psycopg2.DatabaseError) as error:
        print(error)
    finally:
        if conn is not None:
            conn.close()

if __name__ == "__main__":
    seed_categories()
