{"timestamp":"2025-08-06T19:40:27.645174+08:00","level":1,"message":"Test sync task started","fields":{"brand_count":100,"operation":"test_sync","task_id":12345}}
{"timestamp":"2025-08-06T19:40:27.645177+08:00","level":3,"message":"Test batch create failed","fields":{"batch_size":1000,"error":"database connection timeout","operation":"batch_create_brands","platform":"test_platform","task_id":12345}}
{"timestamp":"2025-08-06T19:40:27.645177+08:00","level":2,"message":"Test retry attempt","fields":{"batch_size":500,"error":"connection refused","retry":2,"task_id":12345}}
