package fatcouponlib

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"

	"coupon-backend/infra/external_gateway/fatcouponlib/fatcouponvo"
	"coupon-backend/infra/utils"
)

// BatchGetMerchants fetches all merchants from external API
func BatchGetMerchants(accountName string, accountConfig map[string]interface{}) ([]map[string]interface{}, error) {
	token := accountConfig["token"].(string)
	authorization := accountConfig["authorization"].(string)
	limit := accountConfig["limit"].(int)
	page := 1
	var createDataRows []map[string]interface{}
	uniqueMap := make(map[string]bool)

	for {
		resp, err := getMerchants(token, limit, page)
		if err != nil {
			return nil, fmt.Errorf("failed to get merchants page %d: %w", page, err)
		}
		for _, merchant := range resp.Data.Data {
			// Create unique key using id and slug
			uniqueKey := fmt.Sprintf("%s_%s", merchant.Id, merchant.Slug)
			if uniqueMap[uniqueKey] {
				continue
			}

			// Get category from first category slug
			category := "Others"
			if len(merchant.Categories) > 0 {
				categorySlug := merchant.Categories[0].Slug
				if mappedCategory, exists := CategoryNameMap[categorySlug]; exists {
					category = mappedCategory
				} else {
					category = CategoryNameMap["Others"]
				}
			} else {
				category = CategoryNameMap["Others"]
			}

			rowData := map[string]interface{}{}
			rowData["id"] = merchant.Id
			rowData["unique_name"] = strings.ReplaceAll(strings.ToLower(merchant.Slug+merchant.Id), " ", "")
			rowData["name"] = merchant.Name
			rowData["description"] = merchant.Description
			rowData["category"] = category
			rowData["site_url"] = utils.ExtractDomain(merchant.Domain)

			merchantLogo := merchant.Logo
			if len(merchantLogo) <= 0 {
				merchantLogo = "https://logo.clearbit.com/" + utils.ExtractDomain(merchant.Domain)
			}
			rowData["logo"] = merchantLogo
			rowData["origin_url"] = merchant.Domain
			rowData["country_name"] = merchant.Country
			rowData["supported_countries"] = merchant.Country
			rowData["status"] = 1 // Default to enabled

			// 🚀 使用新的短链接API获取tracking URL
			// 需要对merchant.Domain进行URL编码
			encodedDomain := strings.ReplaceAll(merchant.Domain, "https://", "https%3A%2F%2F")
			encodedDomain = strings.ReplaceAll(encodedDomain, "http://", "http%3A%2F%2F")
			originalTrackingURL := fmt.Sprintf("https://link.fatcoupon.com/redirect?direct=false&url=%s&storeId=%s&partner=", encodedDomain, merchant.Id)
			trackUrl, err := getShortLink(authorization, originalTrackingURL)
			if err != nil {
				trackUrl = originalTrackingURL // 回退到原始URL
			}
			rowData["tracking_url"] = trackUrl
			//rowData["tracking_url"] = originalTrackingURL

			createDataRows = append(createDataRows, rowData)
			uniqueMap[uniqueKey] = true
		}

		if page >= resp.Data.TotalPages {
			break
		}
		page++
	}

	return createDataRows, nil
}

// getMerchants gets merchants from API with pagination
func getMerchants(token string, limit int, page int) (*fatcouponvo.GetMerchantsResp, error) {
	ctx := context.Background()

	params := map[string]interface{}{
		"pageNum":  page,
		"pageSize": limit,
	}

	headers := map[string]string{
		"Cookie":     token,
		"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
	}

	fullURL := host + apiGetMerchants
	resp, err := remoteInvokeWithUrl(ctx, fullURL, http.MethodGet, params, headers, nil)
	if err != nil {
		log.Printf("[ERROR] HTTP request failed: %v", err)
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}

	merchantResp := new(fatcouponvo.GetMerchantsResp)
	if err := json.Unmarshal(resp, merchantResp); err != nil {
		log.Printf("[ERROR] JSON unmarshal failed: %v", err)
		log.Printf("[ERROR] Full response body: %s", string(resp))
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	log.Printf("[HTTP] Successfully parsed response - Errno: %d, Data items: %d",
		merchantResp.Errno, len(merchantResp.Data.Data))

	// Check for API error
	if merchantResp.Errno != 0 {
		return nil, fmt.Errorf("API error: %s (errno: %d)", merchantResp.Errmsg, merchantResp.Errno)
	}

	return merchantResp, nil
}

// getShortLink calls the short links API to get tracking URL
func getShortLink(authorization, originalTrackingURL string) (string, error) {
	ctx := context.Background()

	// 构建请求体
	requestBody := map[string]string{
		"link": originalTrackingURL,
	}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request body: %w", err)
	}

	// 构建请求
	fullURL := host + apiGetShortLinks
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, fullURL, bytes.NewBuffer(jsonBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("accept", "*/*")
	req.Header.Set("accept-language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
	req.Header.Set("authorization", authorization)
	req.Header.Set("cache-control", "no-cache")
	req.Header.Set("content-type", "application/json")
	req.Header.Set("origin", "https://fatcoupon.com")
	req.Header.Set("pragma", "no-cache")
	req.Header.Set("priority", "u=1, i")
	req.Header.Set("referer", "https://fatcoupon.com/")
	req.Header.Set("sec-ch-ua", `"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"`)
	req.Header.Set("sec-ch-ua-mobile", "?0")
	req.Header.Set("sec-ch-ua-platform", `"macOS"`)
	req.Header.Set("sec-fetch-dest", "empty")
	req.Header.Set("sec-fetch-mode", "cors")
	req.Header.Set("sec-fetch-site", "same-site")
	req.Header.Set("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	var responseBody bytes.Buffer
	_, err = responseBody.ReadFrom(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body: %w", err)
	}

	// 解析响应
	var shortLinksResp fatcouponvo.GetShortLinksResp
	if err := json.Unmarshal(responseBody.Bytes(), &shortLinksResp); err != nil {
		log.Printf("[ERROR] JSON unmarshal failed: %v", err)
		log.Printf("[ERROR] Full response body: %s", responseBody.String())
		return "", fmt.Errorf("failed to unmarshal response: %w", err)
	}

	// 检查API错误
	if shortLinksResp.Errno != 0 {
		return "", fmt.Errorf("API error: %s (errno: %d)", shortLinksResp.Errmsg, shortLinksResp.Errno)
	}

	return shortLinksResp.Data.Data, nil
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
