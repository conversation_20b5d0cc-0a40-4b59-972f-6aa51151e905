package persistence

import (
	"coupon-backend/domain/coupon/entity"
	"coupon-backend/domain/coupon/repository"
	"coupon-backend/infra/constant"
	"coupon-backend/infra/ecode"
	"coupon-backend/infra/logger"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type CouponRepositoryImpl struct {
	db *gorm.DB
}

// NewCouponRepository 创建优惠券仓储实现
func NewCouponRepository(db *gorm.DB) repository.CouponRepository {
	return &CouponRepositoryImpl{
		db: db,
	}
}

// GetCouponDetailById 根据ID获取优惠券详情
func (r *CouponRepositoryImpl) GetCouponDetailById(ctx *gin.Context, id uint64) (*entity.Coupon, *ecode.Error) {
	var coupon entity.Coupon
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&coupon).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get coupon by id")
	}
	return &coupon, nil
}

// GetCouponListByCondition 根据条件获取优惠券列表
func (r *CouponRepositoryImpl) GetCouponListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Coupon, int64, *ecode.Error) {
	var coupons []*entity.Coupon
	var total int64

	// 检查是否需要site_url多样性排序（默认开启，除非明确指定了其他排序）
	enableSiteUrlDiversity := true
	if sort, ok := condition["sort"].(string); ok && sort != "" {
		enableSiteUrlDiversity = false
	}

	// 构建基础查询条件
	whereConditions := []string{}
	whereArgs := []interface{}{}

	// 状态过滤
	if status, ok := condition["status"].(int); ok {
		whereConditions = append(whereConditions, "coupons.status = ?")
		whereArgs = append(whereArgs, status)
	}

	// 商家过滤
	if brandID, ok := condition["brand_id"].(uint64); ok && brandID > 0 {
		whereConditions = append(whereConditions, "coupons.brand_id = ?")
		whereArgs = append(whereArgs, brandID)
	}

	// 分类过滤
	if categoryID, ok := condition["category_id"].(uint64); ok && categoryID > 0 {
		whereConditions = append(whereConditions, "coupons.category_id = ?")
		whereArgs = append(whereArgs, categoryID)
	}

	// 推荐过滤
	if isFeatured, ok := condition["is_featured"].(bool); ok && isFeatured {
		whereConditions = append(whereConditions, "coupons.is_featured = ?")
		whereArgs = append(whereArgs, true)
	}

	// 独家过滤
	if isExclusive, ok := condition["is_exclusive"].(bool); ok && isExclusive {
		whereConditions = append(whereConditions, "coupons.is_exclusive = ?")
		whereArgs = append(whereArgs, true)
	}

	// 搜索条件
	if search, ok := condition["search"].(string); ok && search != "" {
		searchPattern := "%" + search + "%"
		whereConditions = append(whereConditions, "(coupons.name ILIKE ? OR coupons.description ILIKE ? OR coupons.discount ILIKE ?)")
		whereArgs = append(whereArgs, searchPattern, searchPattern, searchPattern)
	}

	// 分类Slug过滤（需要特殊处理，因为涉及JOIN）
	hasJoinCategories := false
	if categorySlug, ok := condition["category_slug"].(string); ok && categorySlug != "" {
		whereConditions = append(whereConditions, "categories.slug = ?")
		whereArgs = append(whereArgs, categorySlug)
		hasJoinCategories = true
	}

	// 有效期过滤
	now := time.Now()
	whereConditions = append(whereConditions, "(coupons.end_date IS NULL OR coupons.end_date > ?)")
	whereArgs = append(whereArgs, now)

	// 构建FROM子句（根据是否需要JOIN来决定）
	fromClause := "coupons"
	if hasJoinCategories {
		fromClause = "coupons JOIN categories ON coupons.category_id = categories.id"
	}

	// 构建完整的WHERE子句
	whereClause := ""
	if len(whereConditions) > 0 {
		whereClause = "WHERE " + fmt.Sprintf("%s", whereConditions[0])
		for i := 1; i < len(whereConditions); i++ {
			whereClause += " AND " + whereConditions[i]
		}
	}

	// 先计算总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s %s", fromClause, whereClause)
	if err := r.db.WithContext(ctx).Raw(countQuery, whereArgs...).Scan(&total).Error; err != nil {
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count coupons")
	}

	// 分页参数
	limit := 20
	offset := 0
	if pageSize, ok := condition["page_size"].(int); ok && pageSize > 0 {
		if pageSize > constant.MaxPageSize {
			pageSize = constant.MaxPageSize
		}
		limit = pageSize
	}
	if offsetVal, ok := condition["offset"].(int); ok && offsetVal >= 0 {
		offset = offsetVal
	}

	// 构建主查询
	var mainQuery string
	var finalArgs []interface{}

	if enableSiteUrlDiversity {
		// site_url多样性查询：需要JOIN brands表获取site_url
		// 构建 FROM 子句（包含必需的 JOIN）
		fromClause := "coupons JOIN brands ON coupons.brand_id = brands.id"
		if hasJoinCategories {
			fromClause = "coupons JOIN brands ON coupons.brand_id = brands.id JOIN categories ON coupons.category_id = categories.id"
		}

		// site_url多样性查询：使用CTE和窗口函数
		mainQuery = fmt.Sprintf(`
			WITH ranked_coupons AS (
				SELECT coupons.*,
					ROW_NUMBER() OVER (
						PARTITION BY brands.site_url 
						ORDER BY coupons.is_featured DESC, coupons.is_exclusive DESC, coupons.created_at DESC, coupons.id ASC
					) as site_rank
				FROM %s 
				%s
			)
			SELECT * FROM ranked_coupons
			ORDER BY site_rank ASC, ranked_coupons.created_at DESC, ranked_coupons.id ASC
			LIMIT ? OFFSET ?
		`, fromClause, whereClause)
		finalArgs = append(whereArgs, limit, offset)
	} else {
		// 传统排序查询
		orderClause := r.buildOrderClause(condition)
		mainQuery = fmt.Sprintf(`
			SELECT coupons.* FROM %s %s 
			%s
			LIMIT ? OFFSET ?
		`, fromClause, whereClause, orderClause)
		finalArgs = append(whereArgs, limit, offset)
	}

	// 执行查询
	if err := r.db.WithContext(ctx).Raw(mainQuery, finalArgs...).Scan(&coupons).Error; err != nil {
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get coupons")
	}

	return coupons, total, nil
}

// buildOrderClause 构建ORDER BY子句
func (r *CouponRepositoryImpl) buildOrderClause(condition map[string]interface{}) string {
	if sort, ok := condition["sort"].(string); ok && sort != "" {
		switch sort {
		case constant.SortByCreatedAt:
			return "ORDER BY coupons.created_at DESC, coupons.id ASC"
		case constant.SortByUpdatedAt:
			return "ORDER BY coupons.updated_at DESC, coupons.id ASC"
		case constant.SortByName:
			return "ORDER BY coupons.name ASC, coupons.id ASC"
		case constant.SortByFeatured:
			return "ORDER BY coupons.is_featured DESC, coupons.created_at DESC, coupons.id ASC"
		default:
			return "ORDER BY coupons.created_at DESC, coupons.id ASC"
		}
	}
	return "ORDER BY coupons.created_at DESC, coupons.id ASC"
}

// GetCouponCount 获取优惠券总数
func (r *CouponRepositoryImpl) GetCouponCount(ctx *gin.Context) (int64, *ecode.Error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.Coupon{}).Count(&count).Error
	if err != nil {
		return 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count coupons")
	}
	return count, nil
}

// GetCouponListByIDs 根据ID列表获取优惠券列表
func (r *CouponRepositoryImpl) GetCouponListByIDs(ctx *gin.Context, ids []uint64) ([]*entity.Coupon, *ecode.Error) {
	if len(ids) == 0 {
		return []*entity.Coupon{}, nil
	}

	var coupons []*entity.Coupon
	err := r.db.WithContext(ctx).Where("id IN ?", ids).Find(&coupons).Error
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get coupons by ids")
	}
	return coupons, nil
}

// GetCouponListByBrandIDs 根据商家ID列表获取优惠券列表
func (r *CouponRepositoryImpl) GetCouponListByBrandIDs(ctx *gin.Context, brandIDs []uint64) ([]*entity.Coupon, *ecode.Error) {
	if len(brandIDs) == 0 {
		return []*entity.Coupon{}, nil
	}

	var coupons []*entity.Coupon
	err := r.db.WithContext(ctx).Where("brand_id IN ?", brandIDs).Find(&coupons).Error
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get coupons by brand ids")
	}
	return coupons, nil
}

// CreateCoupon 创建优惠券
func (r *CouponRepositoryImpl) CreateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error {
	err := r.db.WithContext(ctx).Create(coupon).Error
	if err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to create coupon")
	}
	return nil
}

// UpdateCoupon 更新优惠券
func (r *CouponRepositoryImpl) UpdateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error {
	err := r.db.WithContext(ctx).Save(coupon).Error
	if err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to update coupon")
	}
	return nil
}

// DeleteCoupon 删除优惠券
func (r *CouponRepositoryImpl) DeleteCoupon(ctx *gin.Context, id uint64) *ecode.Error {
	result := r.db.WithContext(ctx).Delete(&entity.Coupon{}, id)
	if result.Error != nil {
		return ecode.Wrap(result.Error, ecode.ErrDatabase.Code, "failed to delete coupon")
	}
	if result.RowsAffected == 0 {
		return ecode.ErrNotFound
	}
	return nil
}

// UpdateCouponStatus 更新优惠券状态
func (r *CouponRepositoryImpl) UpdateCouponStatus(ctx *gin.Context, id uint64, status int8) *ecode.Error {
	result := r.db.WithContext(ctx).Model(&entity.Coupon{}).Where("id = ?", id).Update("status", status)
	if result.Error != nil {
		return ecode.Wrap(result.Error, ecode.ErrDatabase.Code, "failed to update coupon status")
	}
	if result.RowsAffected == 0 {
		return ecode.ErrNotFound
	}
	return nil
}

//// BatchCreateCoupons 批量创建优惠券
//func (r *CouponRepositoryImpl) BatchCreateCoupons(ctx *gin.Context, coupons []*entity.Coupon) *ecode.Error {
//	if len(coupons) == 0 {
//		return nil
//	}
//
//	err := r.db.WithContext(ctx).CreateInBatches(coupons, 5000).Error
//	if err != nil {
//		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to batch create coupons")
//	}
//	return nil
//}

// BatchCreateCoupons 批量创建优惠券 for PostgreSQL
func (r *CouponRepositoryImpl) BatchCreateCoupons(ctx *gin.Context, coupons []*entity.Coupon) *ecode.Error {
	if len(coupons) == 0 {
		return nil
	}

	// Dynamic batch size based on data volume - much smaller for PostgreSQL parameter limits
	var batchSize int
	if len(coupons) > 100000 {
		batchSize = 150  // Very small batches for massive datasets
	} else if len(coupons) > 50000 {
		batchSize = 300  // Small batches for very large datasets
	} else if len(coupons) > 10000 {
		batchSize = 600  // Medium batches for large datasets
	} else if len(coupons) > 1000 {
		batchSize = 1200 // Larger batches for medium datasets
	} else {
		batchSize = 2000 // Largest batches for smaller datasets
	}

	// PostgreSQL parameter limit safety check
	// Coupon has ~13 fields, so 65535 / 13 = ~5000 max records per batch
	const maxSafeBatchSize = 4000
	if batchSize > maxSafeBatchSize {
		batchSize = maxSafeBatchSize
	}

	const (
		maxRetries     = 3                      // Maximum retry attempts
		baseRetryDelay = 200 * time.Millisecond // Base delay for exponential backoff
	)

	// Create an optimized GORM session
	session := r.db.WithContext(ctx).Session(&gorm.Session{
		CreateBatchSize:   batchSize,
		PrepareStmt:       true,  // Use prepared statements for performance
		SkipHooks:         true,  // Skip hooks to reduce overhead
		AllowGlobalUpdate: false, // Prevent accidental global updates
	})

	// Wrap the operation in a transaction
	err := session.Transaction(func(tx *gorm.DB) error {
		// Process coupons in batches
		for i := 0; i < len(coupons); i += batchSize {
			end := i + batchSize
			if end > len(coupons) {
				end = len(coupons)
			}
			batch := coupons[i:end]

			// Clean UTF-8 encoding for batch before processing
			for j, coupon := range batch {
				batch[j] = cleanCouponUTF8(coupon)
			}

			// Retry logic for transient errors
			var lastErr error
			for retry := 0; retry < maxRetries; retry++ {
				// Check for context cancellation
				select {
				case <-ctx.Done():
					return ecode.Wrap(ctx.Err(), ecode.ErrDatabase.Code, "context cancelled during batch create")
				default:
				}

				// Perform batch insert - remove conflict handling for now to avoid constraint issues
				if err := tx.CreateInBatches(batch, batchSize).Error; err != nil {
					lastErr = err
					// Log retry attempt
					if asyncLogger := logger.GetGlobalAsyncLogger(); asyncLogger != nil {
						asyncLogger.Warn(fmt.Sprintf("Batch create attempt %d failed, will retry", retry+1), map[string]interface{}{
							"retry":      retry + 1,
							"error":      err.Error(),
							"batch_size": len(batch),
						})
					}
					// Check if error is retryable (PostgreSQL-specific)
					if shouldRetry(err) {
						// Exponential backoff: 200ms, 400ms, 800ms
						time.Sleep(baseRetryDelay * time.Duration(1<<retry))
						continue
					}
					return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to create coupon batch")
				}

				// Success, clear error and break retry loop
				lastErr = nil
				break
			}

			// If retries exhausted, return the last error
			if lastErr != nil {
				if asyncLogger := logger.GetGlobalAsyncLogger(); asyncLogger != nil {
					asyncLogger.Error(fmt.Sprintf("Failed to create coupon batch after %d retries", maxRetries), map[string]interface{}{
						"batch_start": i + 1,
						"batch_end":   end,
						"error":       lastErr.Error(),
					})
				}
				return ecode.Wrap(lastErr, ecode.ErrDatabase.Code, "failed to create coupon batch after max retries")
			}
		}

		return nil
	})

	if err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to batch create coupons")
	}

	return nil
}

// BatchUpdateCoupons 批量更新优惠券
func (r *CouponRepositoryImpl) BatchUpdateCoupons(ctx *gin.Context, coupons []*entity.Coupon) *ecode.Error {
	if len(coupons) == 0 {
		return nil
	}

	tx := r.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, coupon := range coupons {
		if err := tx.Save(coupon).Error; err != nil {
			tx.Rollback()
			return ecode.Wrap(err, ecode.ErrDatabase.Code, fmt.Sprintf("failed to update coupon %d", coupon.ID))
		}
	}

	if err := tx.Commit().Error; err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to commit batch update")
	}

	return nil
}

// GetCouponsByPlatform 根据平台类型获取优惠券列表
func (r *CouponRepositoryImpl) GetCouponsByPlatform(ctx *gin.Context, platformType string) ([]*entity.Coupon, *ecode.Error) {
	var coupons []*entity.Coupon
	err := r.db.WithContext(ctx).Where("platform_type = ?", platformType).Find(&coupons).Error
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get coupons by platform")
	}
	return coupons, nil
}

// FindCouponByPlatformAndCouponID 根据平台类型和优惠券ID查找优惠券
func (r *CouponRepositoryImpl) FindCouponByPlatformAndCouponID(ctx *gin.Context, platformType, couponID string) (*entity.Coupon, *ecode.Error) {
	var coupon entity.Coupon
	err := r.db.WithContext(ctx).Where("platform_type = ? AND platform_coupon_id = ?", platformType, couponID).First(&coupon).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to find coupon by platform and coupon id")
	}
	return &coupon, nil
}

// FindCouponsByPlatformAndCouponIDs 根据平台类型和优惠券ID列表批量查找优惠券
func (r *CouponRepositoryImpl) FindCouponsByPlatformAndCouponIDs(ctx *gin.Context, platformType string, couponIDs []string) ([]*entity.Coupon, *ecode.Error) {
	if len(couponIDs) == 0 {
		return []*entity.Coupon{}, nil
	}

	var coupons []*entity.Coupon
	err := r.db.WithContext(ctx).Where("platform_type = ? AND platform_coupon_id IN ?", platformType, couponIDs).Find(&coupons).Error
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to find coupons by platform and coupon ids")
	}
	return coupons, nil
}

// cleanCouponUTF8 cleans invalid UTF-8 characters from coupon data
func cleanCouponUTF8(coupon *entity.Coupon) *entity.Coupon {
	if coupon == nil {
		return coupon
	}
	
	// Clean all string fields
	coupon.Code = cleanUTF8String(coupon.Code)
	coupon.Name = cleanUTF8String(coupon.Name)
	coupon.Description = cleanUTF8String(coupon.Description)
	coupon.Discount = cleanUTF8String(coupon.Discount)
	coupon.PlatformType = cleanUTF8String(coupon.PlatformType)
	coupon.PlatformCouponID = cleanUTF8String(coupon.PlatformCouponID)
	
	return coupon
}