package persistence

import (
	"coupon-backend/domain/deal/entity"
	"coupon-backend/domain/deal/repository"
	"coupon-backend/infra/constant"
	"coupon-backend/infra/ecode"
	"coupon-backend/infra/logger"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type DealRepositoryImpl struct {
	db *gorm.DB
}

// NewDealRepository 创建优惠活动仓储实现
func NewDealRepository(db *gorm.DB) repository.DealRepository {
	return &DealRepositoryImpl{
		db: db,
	}
}

// GetDealDetailById 根据ID获取优惠活动详情
func (r *DealRepositoryImpl) GetDealDetailById(ctx *gin.Context, id uint64) (*entity.Deal, *ecode.Error) {
	var deal entity.Deal
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&deal).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get deal by id")
	}
	return &deal, nil
}

// GetDealListByCondition 根据条件获取优惠活动列表
func (r *DealRepositoryImpl) GetDealListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Deal, int64, *ecode.Error) {
	var deals []*entity.Deal
	var total int64

	// 检查是否需要site_url多样性排序（默认开启，除非明确指定了其他排序）
	enableSiteUrlDiversity := true
	if sort, ok := condition["sort"].(string); ok && sort != "" {
		enableSiteUrlDiversity = false
	}

	// 构建基础查询条件
	whereConditions := []string{}
	whereArgs := []interface{}{}

	// 状态过滤
	if status, ok := condition["status"].(int); ok {
		whereConditions = append(whereConditions, "deals.status = ?")
		whereArgs = append(whereArgs, status)
	}

	// 商家过滤
	if brandID, ok := condition["brand_id"].(uint64); ok && brandID > 0 {
		whereConditions = append(whereConditions, "deals.brand_id = ?")
		whereArgs = append(whereArgs, brandID)
	}

	// 分类过滤
	if categoryID, ok := condition["category_id"].(uint64); ok && categoryID > 0 {
		whereConditions = append(whereConditions, "deals.category_id = ?")
		whereArgs = append(whereArgs, categoryID)
	}

	// 热门活动过滤
	if isHotDeal, ok := condition["is_hot_deal"].(bool); ok && isHotDeal {
		whereConditions = append(whereConditions, "deals.is_hot_deal = ?")
		whereArgs = append(whereArgs, true)
	}

	// 推荐过滤
	if isFeatured, ok := condition["is_featured"].(bool); ok && isFeatured {
		whereConditions = append(whereConditions, "deals.is_featured = ?")
		whereArgs = append(whereArgs, true)
	}

	// 搜索条件
	if search, ok := condition["search"].(string); ok && search != "" {
		searchPattern := "%" + search + "%"
		whereConditions = append(whereConditions, "(deals.title ILIKE ? OR deals.description ILIKE ? OR deals.discount ILIKE ?)")
		whereArgs = append(whereArgs, searchPattern, searchPattern, searchPattern)
	}

	// 分类Slug过滤（需要特殊处理，因为涉及JOIN）
	hasJoinCategories := false
	if categorySlug, ok := condition["category_slug"].(string); ok && categorySlug != "" {
		whereConditions = append(whereConditions, "categories.slug = ?")
		whereArgs = append(whereArgs, categorySlug)
		hasJoinCategories = true
	}

	// 有效期过滤
	now := time.Now()
	whereConditions = append(whereConditions, "(deals.end_date IS NULL OR deals.end_date > ?)")
	whereArgs = append(whereArgs, now)

	// 构建FROM子句（根据是否需要JOIN来决定）
	fromClause := "deals"
	if hasJoinCategories {
		fromClause = "deals JOIN categories ON deals.category_id = categories.id"
	}

	// 构建完整的WHERE子句
	whereClause := ""
	if len(whereConditions) > 0 {
		whereClause = "WHERE " + fmt.Sprintf("%s", whereConditions[0])
		for i := 1; i < len(whereConditions); i++ {
			whereClause += " AND " + whereConditions[i]
		}
	}

	// 先计算总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s %s", fromClause, whereClause)
	if err := r.db.WithContext(ctx).Raw(countQuery, whereArgs...).Scan(&total).Error; err != nil {
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count deals")
	}

	// 分页参数
	limit := 20
	offset := 0
	if pageSize, ok := condition["page_size"].(int); ok && pageSize > 0 {
		if pageSize > constant.MaxPageSize {
			pageSize = constant.MaxPageSize
		}
		limit = pageSize
	}
	if offsetVal, ok := condition["offset"].(int); ok && offsetVal >= 0 {
		offset = offsetVal
	}

	// 构建主查询
	var mainQuery string
	var finalArgs []interface{}

	if enableSiteUrlDiversity {
		// site_url多样性查询：需要JOIN brands表获取site_url
		// 构建 FROM 子句（包含必需的 JOIN）
		fromClause := "deals JOIN brands ON deals.brand_id = brands.id"
		if hasJoinCategories {
			fromClause = "deals JOIN brands ON deals.brand_id = brands.id JOIN categories ON deals.category_id = categories.id"
		}

		// site_url多样性查询：使用CTE和窗口函数
		mainQuery = fmt.Sprintf(`
			WITH ranked_deals AS (
				SELECT deals.*,
					ROW_NUMBER() OVER (
						PARTITION BY brands.site_url 
						ORDER BY deals.is_featured DESC, deals.is_hot_deal DESC, deals.created_at DESC, deals.id ASC
					) as site_rank
				FROM %s 
				%s
			)
			SELECT * FROM ranked_deals
			ORDER BY site_rank ASC, created_at DESC, id ASC
			LIMIT ? OFFSET ?
		`, fromClause, whereClause)
		finalArgs = append(whereArgs, limit, offset)
	} else {
		// 传统排序查询
		orderClause := r.buildOrderClause(condition)
		mainQuery = fmt.Sprintf(`
			SELECT deals.* FROM %s %s 
			%s
			LIMIT ? OFFSET ?
		`, fromClause, whereClause, orderClause)
		finalArgs = append(whereArgs, limit, offset)
	}

	// 执行查询
	if err := r.db.WithContext(ctx).Raw(mainQuery, finalArgs...).Scan(&deals).Error; err != nil {
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get deals")
	}

	return deals, total, nil
}

// buildOrderClause 构建ORDER BY子句
func (r *DealRepositoryImpl) buildOrderClause(condition map[string]interface{}) string {
	if sort, ok := condition["sort"].(string); ok && sort != "" {
		switch sort {
		case constant.SortByCreatedAt:
			return "ORDER BY deals.created_at DESC, deals.id ASC"
		case constant.SortByUpdatedAt:
			return "ORDER BY deals.updated_at DESC, deals.id ASC"
		case constant.SortByName:
			return "ORDER BY deals.title ASC, deals.id ASC"
		case constant.SortByFeatured:
			return "ORDER BY deals.is_featured DESC, deals.is_hot_deal DESC, deals.created_at DESC, deals.id ASC"
		default:
			return "ORDER BY deals.created_at DESC, deals.id ASC"
		}
	}
	return "ORDER BY deals.created_at DESC, deals.id ASC"
}

// GetDealCount 获取优惠活动总数
func (r *DealRepositoryImpl) GetDealCount(ctx *gin.Context) (int64, *ecode.Error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.Deal{}).Count(&count).Error
	if err != nil {
		return 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count deals")
	}
	return count, nil
}

// GetDealListByIDs 根据ID列表获取优惠活动列表
func (r *DealRepositoryImpl) GetDealListByIDs(ctx *gin.Context, ids []uint64) ([]*entity.Deal, *ecode.Error) {
	if len(ids) == 0 {
		return []*entity.Deal{}, nil
	}

	var deals []*entity.Deal
	err := r.db.WithContext(ctx).Where("id IN ?", ids).Find(&deals).Error
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get deals by ids")
	}
	return deals, nil
}

// GetDealListByBrandIDs 根据商家ID列表获取优惠活动列表
func (r *DealRepositoryImpl) GetDealListByBrandIDs(ctx *gin.Context, brandIDs []uint64) ([]*entity.Deal, *ecode.Error) {
	if len(brandIDs) == 0 {
		return []*entity.Deal{}, nil
	}

	var deals []*entity.Deal
	err := r.db.WithContext(ctx).Where("brand_id IN ?", brandIDs).Find(&deals).Error
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get deals by brand ids")
	}
	return deals, nil
}

// CreateDeal 创建优惠活动
func (r *DealRepositoryImpl) CreateDeal(ctx *gin.Context, deal *entity.Deal) *ecode.Error {
	err := r.db.WithContext(ctx).Create(deal).Error
	if err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to create deal")
	}
	return nil
}

// UpdateDeal 更新优惠活动
func (r *DealRepositoryImpl) UpdateDeal(ctx *gin.Context, deal *entity.Deal) *ecode.Error {
	err := r.db.WithContext(ctx).Save(deal).Error
	if err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to update deal")
	}
	return nil
}

// DeleteDeal 删除优惠活动
func (r *DealRepositoryImpl) DeleteDeal(ctx *gin.Context, id uint64) *ecode.Error {
	result := r.db.WithContext(ctx).Delete(&entity.Deal{}, id)
	if result.Error != nil {
		return ecode.Wrap(result.Error, ecode.ErrDatabase.Code, "failed to delete deal")
	}
	if result.RowsAffected == 0 {
		return ecode.ErrNotFound
	}
	return nil
}

// UpdateDealStatus 更新优惠活动状态
func (r *DealRepositoryImpl) UpdateDealStatus(ctx *gin.Context, id uint64, status int8) *ecode.Error {
	result := r.db.WithContext(ctx).Model(&entity.Deal{}).Where("id = ?", id).Update("status", status)
	if result.Error != nil {
		return ecode.Wrap(result.Error, ecode.ErrDatabase.Code, "failed to update deal status")
	}
	if result.RowsAffected == 0 {
		return ecode.ErrNotFound
	}
	return nil
}

// BatchCreateDeals 批量创建优惠活动
func (r *DealRepositoryImpl) BatchCreateDeals(ctx *gin.Context, deals []*entity.Deal) *ecode.Error {
	if len(deals) == 0 {
		return nil
	}

	// Dynamic batch size based on data volume - much smaller for PostgreSQL parameter limits
	var batchSize int
	if len(deals) > 50000 {
		batchSize = 200  // Very small batches for huge datasets
	} else if len(deals) > 10000 {
		batchSize = 500  // Small batches for large datasets
	} else if len(deals) > 1000 {
		batchSize = 1000 // Medium batches for medium datasets
	} else {
		batchSize = 2000 // Larger batches for smaller datasets
	}

	// PostgreSQL parameter limit safety check
	// Deal has ~15 fields, so 65535 / 15 = ~4000 max records per batch
	const maxSafeBatchSize = 3500
	if batchSize > maxSafeBatchSize {
		batchSize = maxSafeBatchSize
	}

	const (
		maxRetries     = 3                      // Maximum retry attempts
		baseRetryDelay = 200 * time.Millisecond // Base delay for exponential backoff
	)

	// Create an optimized GORM session
	session := r.db.WithContext(ctx).Session(&gorm.Session{
		CreateBatchSize:   batchSize,
		PrepareStmt:       true,  // Use prepared statements for performance
		SkipHooks:         true,  // Skip hooks to reduce overhead
		AllowGlobalUpdate: false, // Prevent accidental global updates
	})

	// Wrap the operation in a transaction
	err := session.Transaction(func(tx *gorm.DB) error {
		// Process deals in batches
		for i := 0; i < len(deals); i += batchSize {
			end := i + batchSize
			if end > len(deals) {
				end = len(deals)
			}
			batch := deals[i:end]

			// Clean UTF-8 encoding for batch before processing
			for j, deal := range batch {
				batch[j] = cleanDealUTF8(deal)
			}

			// Retry logic for transient errors
			var lastErr error
			for retry := 0; retry < maxRetries; retry++ {
				// Check for context cancellation
				select {
				case <-ctx.Done():
					return ecode.Wrap(ctx.Err(), ecode.ErrDatabase.Code, "context cancelled during batch create")
				default:
				}

				// Perform batch insert with conflict handling
				if err := tx.Clauses(
					clause.OnConflict{
						Columns:   []clause.Column{{Name: "platform_type"}, {Name: "platform_deal_id"}},
						DoNothing: true, // Skip duplicates
					},
				).CreateInBatches(batch, batchSize).Error; err != nil {
					lastErr = err
					// Log retry attempt
					if asyncLogger := logger.GetGlobalAsyncLogger(); asyncLogger != nil {
						asyncLogger.Warn(fmt.Sprintf("Batch create attempt %d failed, will retry", retry+1), map[string]interface{}{
							"retry":     retry + 1,
							"error":     err.Error(),
							"batch_size": len(batch),
						})
					}
					// Check if error is retryable (PostgreSQL-specific)
					if shouldRetry(err) {
						// Exponential backoff: 200ms, 400ms, 800ms
						time.Sleep(baseRetryDelay * time.Duration(1<<retry))
						continue
					}
					return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to create deals batch")
				}

				// Success, clear error and break retry loop
				lastErr = nil
				break
			}

			// If retries exhausted, return the last error
			if lastErr != nil {
				if asyncLogger := logger.GetGlobalAsyncLogger(); asyncLogger != nil {
					asyncLogger.Error(fmt.Sprintf("Failed to create deal batch after %d retries", maxRetries), map[string]interface{}{
						"batch_start": i + 1,
						"batch_end":   end,
						"error":       lastErr.Error(),
					})
				}
				return ecode.Wrap(lastErr, ecode.ErrDatabase.Code, "failed to create deals batch after max retries")
			}
		}

		return nil
	})

	if err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to batch create deals")
	}

	return nil
}

// BatchUpdateDeals 批量更新优惠活动
func (r *DealRepositoryImpl) BatchUpdateDeals(ctx *gin.Context, deals []*entity.Deal) *ecode.Error {
	if len(deals) == 0 {
		return nil
	}

	tx := r.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, deal := range deals {
		if err := tx.Save(deal).Error; err != nil {
			tx.Rollback()
			return ecode.Wrap(err, ecode.ErrDatabase.Code, fmt.Sprintf("failed to update deal %d", deal.ID))
		}
	}

	if err := tx.Commit().Error; err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to commit batch update")
	}

	return nil
}

// GetDealsByPlatform 根据平台类型获取优惠活动列表
func (r *DealRepositoryImpl) GetDealsByPlatform(ctx *gin.Context, platformType string) ([]*entity.Deal, *ecode.Error) {
	var deals []*entity.Deal
	err := r.db.WithContext(ctx).Where("platform_type = ?", platformType).Find(&deals).Error
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get deals by platform")
	}
	return deals, nil
}

// FindDealByPlatformAndDealID 根据平台类型和优惠活动ID查找优惠活动
func (r *DealRepositoryImpl) FindDealByPlatformAndDealID(ctx *gin.Context, platformType, dealID string) (*entity.Deal, *ecode.Error) {
	var deal entity.Deal
	err := r.db.WithContext(ctx).Where("platform_type = ? AND platform_deal_id = ?", platformType, dealID).First(&deal).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to find deal by platform and deal id")
	}
	return &deal, nil
}

// FindDealsByPlatformAndDealIDs 根据平台类型和优惠活动ID列表批量查找优惠活动
func (r *DealRepositoryImpl) FindDealsByPlatformAndDealIDs(ctx *gin.Context, platformType string, dealIDs []string) ([]*entity.Deal, *ecode.Error) {
	if len(dealIDs) == 0 {
		return []*entity.Deal{}, nil
	}

	var deals []*entity.Deal
	err := r.db.WithContext(ctx).Where("platform_type = ? AND platform_deal_id IN ?", platformType, dealIDs).Find(&deals).Error
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to find deals by platform and deal ids")
	}
	return deals, nil
}

// cleanDealUTF8 cleans invalid UTF-8 characters from deal data
func cleanDealUTF8(deal *entity.Deal) *entity.Deal {
	if deal == nil {
		return deal
	}
	
	// Clean all string fields
	deal.Title = cleanUTF8String(deal.Title)
	deal.Description = cleanUTF8String(deal.Description)
	deal.Discount = cleanUTF8String(deal.Discount)
	deal.PlatformType = cleanUTF8String(deal.PlatformType)
	deal.PlatformDealID = cleanUTF8String(deal.PlatformDealID)
	
	return deal
}
