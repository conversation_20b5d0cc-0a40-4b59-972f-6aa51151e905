package persistence

import (
	"coupon-backend/domain/brand/entity"
	"coupon-backend/domain/brand/repository"
	"coupon-backend/infra/constant"
	"coupon-backend/infra/ecode"
	"coupon-backend/infra/logger"
	"fmt"
	"gorm.io/gorm/clause"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type BrandRepositoryImpl struct {
	db *gorm.DB
}

// NewBrandRepository 创建商家仓储实现
func NewBrandRepository(db *gorm.DB) repository.BrandRepository {
	return &BrandRepositoryImpl{
		db: db,
	}
}

// GetBrandDetailById 根据ID获取商家详情
func (r *BrandRepositoryImpl) GetBrandDetailById(ctx *gin.Context, id uint64) (*entity.Brand, *ecode.Error) {
	var brand entity.Brand
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&brand).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get brand by id")
	}
	return &brand, nil
}

// GetBrandDetailByUniqueName 根据UniqueName获取商家详情
func (r *BrandRepositoryImpl) GetBrandDetailByUniqueName(ctx *gin.Context, uniqueName string) (*entity.Brand, *ecode.Error) {
	var brand entity.Brand
	err := r.db.WithContext(ctx).Where("unique_name = ?", uniqueName).First(&brand).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get brand by unique name")
	}
	return &brand, nil
}

// GetBrandListByCondition 根据条件获取商家列表
func (r *BrandRepositoryImpl) GetBrandListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Brand, int64, *ecode.Error) {
	// 检查是否需要site_url多样性排序（默认开启，除非明确指定了其他排序）
	enableSiteUrlDiversity := true
	if sort, ok := condition["sort"].(string); ok && sort != "" {
		enableSiteUrlDiversity = false
	}

	if enableSiteUrlDiversity {
		// 使用site_url多样性查询
		return r.getBrandListWithSiteUrlDiversity(ctx, condition)
	} else {
		// 使用传统查询
		return r.getBrandListTraditional(ctx, condition)
	}
}

// getBrandListWithSiteUrlDiversity 获取具有site_url多样性的品牌列表
func (r *BrandRepositoryImpl) getBrandListWithSiteUrlDiversity(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Brand, int64, *ecode.Error) {
	var brands []*entity.Brand
	var total int64

	// 构建基础查询条件
	whereConditions := []string{}
	whereArgs := []interface{}{}

	// 状态过滤
	if status, ok := condition["status"].(int); ok {
		whereConditions = append(whereConditions, "status = ?")
		whereArgs = append(whereArgs, status)
	}

	// 分类过滤
	if categoryID, ok := condition["category_id"].(uint64); ok && categoryID > 0 {
		whereConditions = append(whereConditions, "category_id = ?")
		whereArgs = append(whereArgs, categoryID)
	}

	// 推荐过滤
	if featured, ok := condition["featured"].(bool); ok && featured {
		whereConditions = append(whereConditions, "featured = ?")
		whereArgs = append(whereArgs, true)
	}

	// 搜索条件
	if search, ok := condition["search"].(string); ok && search != "" {
		searchPattern := "%" + search + "%"
		whereConditions = append(whereConditions, "(name ILIKE ? OR unique_name ILIKE ? OR description ILIKE ?)")
		whereArgs = append(whereArgs, searchPattern, searchPattern, searchPattern)
	}

	// 首字母过滤
	if startsWith, ok := condition["starts_with"].(string); ok && startsWith != "" {
		whereConditions = append(whereConditions, "name ILIKE ?")
		whereArgs = append(whereArgs, startsWith+"%")
	}

	// 构建完整的WHERE子句
	whereClause := ""
	if len(whereConditions) > 0 {
		whereClause = "WHERE " + fmt.Sprintf("%s", whereConditions[0])
		for i := 1; i < len(whereConditions); i++ {
			whereClause += " AND " + whereConditions[i]
		}
	}

	// 先计算总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM brands %s", whereClause)
	if err := r.db.WithContext(ctx).Raw(countQuery, whereArgs...).Scan(&total).Error; err != nil {
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count brands")
	}

	// 分页参数
	limit := 20
	offset := 0
	if pageSize, ok := condition["page_size"].(int); ok && pageSize > 0 {
		if pageSize > constant.MaxPageSize {
			pageSize = constant.MaxPageSize
		}
		limit = pageSize
	}
	if offsetVal, ok := condition["offset"].(int); ok && offsetVal >= 0 {
		offset = offsetVal
	}

	// site_url多样性查询：使用CTE和窗口函数，优先展示不同site_url的品牌
	// 添加优先级排序逻辑：优先显示coupon和deal数量都大于0的，其次是其中一个大于0的，最后是都为0的
	mainQuery := fmt.Sprintf(`
		WITH ranked_brands AS (
			SELECT brands.*,
				-- 计算优惠券和优惠活动的优先级分数：两个都有(3) > 其中一个有(2) > 都没有(1)
				CASE 
					WHEN brands.total_coupons > 0 AND brands.total_deals > 0 THEN 3
					WHEN brands.total_coupons > 0 OR brands.total_deals > 0 THEN 2
					ELSE 1
				END as priority_score,
				ROW_NUMBER() OVER (
					PARTITION BY brands.site_url 
					ORDER BY 
						-- 首先按优先级分数排序
						CASE 
							WHEN brands.total_coupons > 0 AND brands.total_deals > 0 THEN 3
							WHEN brands.total_coupons > 0 OR brands.total_deals > 0 THEN 2
							ELSE 1
						END DESC,
						brands.featured DESC, 
						brands.platform_priority ASC, 
						brands.created_at DESC, 
						brands.id ASC
				) as site_rank
			FROM brands 
			%s
		)
		SELECT * FROM ranked_brands
		ORDER BY site_rank ASC, priority_score DESC, site_url ASC, featured DESC, created_at DESC, id ASC
		LIMIT ? OFFSET ?
	`, whereClause)
	finalArgs := append(whereArgs, limit, offset)

	// 执行查询
	if err := r.db.WithContext(ctx).Raw(mainQuery, finalArgs...).Scan(&brands).Error; err != nil {
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get brands")
	}

	return brands, total, nil
}

// getBrandListTraditional 获取传统的品牌列表（保持原有逻辑）
func (r *BrandRepositoryImpl) getBrandListTraditional(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Brand, int64, *ecode.Error) {
	var brands []*entity.Brand
	var total int64

	query := r.db.WithContext(ctx).Model(&entity.Brand{})

	// 状态过滤
	if status, ok := condition["status"].(int); ok {
		query = query.Where("status = ?", status)
	}

	// 分类过滤
	if categoryID, ok := condition["category_id"].(uint64); ok && categoryID > 0 {
		query = query.Where("category_id = ?", categoryID)
	}

	// 推荐过滤
	if featured, ok := condition["featured"].(bool); ok && featured {
		query = query.Where("featured = ?", true)
	}

	// 搜索条件
	if search, ok := condition["search"].(string); ok && search != "" {
		searchPattern := "%" + search + "%"
		query = query.Where("name ILIKE ? OR unique_name ILIKE ? OR description ILIKE ?", searchPattern, searchPattern, searchPattern)
	}

	// 首字母过滤
	if startsWith, ok := condition["starts_with"].(string); ok && startsWith != "" {
		query = query.Where("name ILIKE ?", startsWith+"%")
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count brands")
	}

	// 排序 - 添加优先级排序逻辑
	if sort, ok := condition["sort"].(string); ok && sort != "" {
		switch sort {
		case constant.SortByCreatedAt:
			// 在按创建时间排序时也应用优先级逻辑
			query = query.Order(`
				CASE 
					WHEN total_coupons > 0 AND total_deals > 0 THEN 3
					WHEN total_coupons > 0 OR total_deals > 0 THEN 2
					ELSE 1
				END DESC, created_at DESC, id ASC`)
		case constant.SortByUpdatedAt:
			query = query.Order(`
				CASE 
					WHEN total_coupons > 0 AND total_deals > 0 THEN 3
					WHEN total_coupons > 0 OR total_deals > 0 THEN 2
					ELSE 1
				END DESC, updated_at DESC, id ASC`)
		case constant.SortByName:
			query = query.Order(`
				CASE 
					WHEN total_coupons > 0 AND total_deals > 0 THEN 3
					WHEN total_coupons > 0 OR total_deals > 0 THEN 2
					ELSE 1
				END DESC, name ASC, id ASC`)
		case constant.SortByFeatured:
			query = query.Order(`
				CASE 
					WHEN total_coupons > 0 AND total_deals > 0 THEN 3
					WHEN total_coupons > 0 OR total_deals > 0 THEN 2
					ELSE 1
				END DESC, featured DESC, created_at DESC, id ASC`)
		default:
			query = query.Order(`
				CASE 
					WHEN total_coupons > 0 AND total_deals > 0 THEN 3
					WHEN total_coupons > 0 OR total_deals > 0 THEN 2
					ELSE 1
				END DESC, created_at DESC, id ASC`)
		}
	} else {
		// 默认排序也应用优先级逻辑
		query = query.Order(`
			CASE 
				WHEN total_coupons > 0 AND total_deals > 0 THEN 3
				WHEN total_coupons > 0 OR total_deals > 0 THEN 2
				ELSE 1
			END DESC, created_at DESC, id ASC`)
	}

	// 分页
	if pageSize, ok := condition["page_size"].(int); ok && pageSize > 0 {
		if pageSize > constant.MaxPageSize {
			pageSize = constant.MaxPageSize
		}
		query = query.Limit(pageSize)
	}

	if offset, ok := condition["offset"].(int); ok && offset >= 0 {
		query = query.Offset(offset)
	}

	// 执行查询
	if err := query.Find(&brands).Error; err != nil {
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get brands")
	}

	return brands, total, nil
}

// GetBrandCount 获取商家总数
func (r *BrandRepositoryImpl) GetBrandCount(ctx *gin.Context) (int64, *ecode.Error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.Brand{}).Count(&count).Error
	if err != nil {
		return 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count brands")
	}
	return count, nil
}

// GetBrandListByIDs 根据ID列表获取商家列表
func (r *BrandRepositoryImpl) GetBrandListByIDs(ctx *gin.Context, ids []uint64) ([]*entity.Brand, *ecode.Error) {
	if len(ids) == 0 {
		return []*entity.Brand{}, nil
	}

	var brands []*entity.Brand
	err := r.db.WithContext(ctx).Where("id IN ?", ids).Find(&brands).Error
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get brands by ids")
	}
	return brands, nil
}

// CreateBrand 创建商家
func (r *BrandRepositoryImpl) CreateBrand(ctx *gin.Context, brand *entity.Brand) *ecode.Error {
	err := r.db.WithContext(ctx).Create(brand).Error
	if err != nil {
		if strings.Contains(err.Error(), "duplicate key") {
			return ecode.New(ecode.ErrInvalidParameter.Code, "brand unique name already exists")
		}
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to create brand")
	}
	return nil
}

// UpdateBrand 更新商家
func (r *BrandRepositoryImpl) UpdateBrand(ctx *gin.Context, brand *entity.Brand) *ecode.Error {
	err := r.db.WithContext(ctx).Save(brand).Error
	if err != nil {
		if strings.Contains(err.Error(), "duplicate key") {
			return ecode.New(ecode.ErrInvalidParameter.Code, "brand unique name already exists")
		}
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to update brand")
	}
	return nil
}

// DeleteBrand 删除商家
func (r *BrandRepositoryImpl) DeleteBrand(ctx *gin.Context, id uint64) *ecode.Error {
	result := r.db.WithContext(ctx).Delete(&entity.Brand{}, id)
	if result.Error != nil {
		return ecode.Wrap(result.Error, ecode.ErrDatabase.Code, "failed to delete brand")
	}
	if result.RowsAffected == 0 {
		return ecode.ErrNotFound
	}
	return nil
}

// UpdateBrandStatus 更新商家状态
func (r *BrandRepositoryImpl) UpdateBrandStatus(ctx *gin.Context, id uint64, status int8) *ecode.Error {
	result := r.db.WithContext(ctx).Model(&entity.Brand{}).Where("id = ?", id).Update("status", status)
	if result.Error != nil {
		return ecode.Wrap(result.Error, ecode.ErrDatabase.Code, "failed to update brand status")
	}
	if result.RowsAffected == 0 {
		return ecode.ErrNotFound
	}
	return nil
}

// BatchCreateBrands 批量创建商家
//func (r *BrandRepositoryImpl) BatchCreateBrands(ctx *gin.Context, brands []*entity.Brand) *ecode.Error {
//	if len(brands) == 0 {
//		return nil
//	}
//
//	err := r.db.WithContext(ctx).CreateInBatches(brands, 5000).Error
//	if err != nil {
//		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to batch create brands")
//	}
//	return nil
//}

func (r *BrandRepositoryImpl) BatchCreateBrands(ctx *gin.Context, brands []*entity.Brand) *ecode.Error {
	if len(brands) == 0 {
		return nil
	}

	// Dynamic batch size based on data volume - much smaller for PostgreSQL parameter limits
	var batchSize int
	if len(brands) > 50000 {
		batchSize = 200  // Very small batches for huge datasets
	} else if len(brands) > 10000 {
		batchSize = 500  // Small batches for large datasets
	} else if len(brands) > 1000 {
		batchSize = 1000 // Medium batches for medium datasets
	} else {
		batchSize = 2000 // Larger batches for smaller datasets
	}

	// PostgreSQL parameter limit safety check
	// Brand has ~13 fields, so 65535 / 13 = ~5000 max records per batch
	const maxSafeBatchSize = 4000
	if batchSize > maxSafeBatchSize {
		batchSize = maxSafeBatchSize
	}

	const (
		maxRetries     = 3                      // Maximum retry attempts
		baseRetryDelay = 200 * time.Millisecond // Base delay for exponential backoff
	)

	// Create an optimized GORM session
	session := r.db.WithContext(ctx).Session(&gorm.Session{
		CreateBatchSize:   batchSize,
		PrepareStmt:       true,  // Use prepared statements for performance
		SkipHooks:         true,  // Skip hooks to reduce overhead
		AllowGlobalUpdate: false, // Prevent accidental global updates
	})

	// Wrap the operation in a transaction
	err := session.Transaction(func(tx *gorm.DB) error {
		// Process brands in batches
		for i := 0; i < len(brands); i += batchSize {
			end := i + batchSize
			if end > len(brands) {
				end = len(brands)
			}
			batch := brands[i:end]

			// Clean UTF-8 encoding for batch before processing
			for j, brand := range batch {
				batch[j] = cleanBrandUTF8(brand)
			}

			// Retry logic for transient errors
			var lastErr error
			for retry := 0; retry < maxRetries; retry++ {
				// Check for context cancellation
				select {
				case <-ctx.Done():
					return ecode.Wrap(ctx.Err(), ecode.ErrDatabase.Code, "context cancelled during batch create")
				default:
				}

				// Perform batch insert with conflict handling using correct unique constraint
				if err := tx.Clauses(
					clause.OnConflict{
						Columns:   []clause.Column{{Name: "unique_name"}},
						DoNothing: true, // Skip duplicates
					},
				).CreateInBatches(batch, batchSize).Error; err != nil {
					lastErr = err
					// Log retry attempt
					if asyncLogger := logger.GetGlobalAsyncLogger(); asyncLogger != nil {
						asyncLogger.Warn(fmt.Sprintf("Batch create attempt %d failed, will retry", retry+1), map[string]interface{}{
							"retry":     retry + 1,
							"error":     err.Error(),
							"batch_size": len(batch),
						})
					}
					// Check if error is retryable (PostgreSQL-specific)
					if shouldRetry(err) {
						// Exponential backoff: 200ms, 400ms, 800ms
						time.Sleep(baseRetryDelay * time.Duration(1<<retry))
						continue
					}
					return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to create brands batch")
				}

				// Success, clear error and break retry loop
				lastErr = nil
				break
			}

			// If retries exhausted, return the last error
			if lastErr != nil {
				if asyncLogger := logger.GetGlobalAsyncLogger(); asyncLogger != nil {
					asyncLogger.Error(fmt.Sprintf("Failed to create brand batch after %d retries", maxRetries), map[string]interface{}{
						"batch_start": i + 1,
						"batch_end":   end,
						"error":       lastErr.Error(),
					})
				}
				return ecode.Wrap(lastErr, ecode.ErrDatabase.Code, "failed to create brands batch after max retries")
			}
		}

		return nil
	})

	if err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to batch create brands")
	}

	return nil
}

// BatchUpdateBrands 批量更新商家
func (r *BrandRepositoryImpl) BatchUpdateBrands(ctx *gin.Context, brands []*entity.Brand) *ecode.Error {
	if len(brands) == 0 {
		return nil
	}

	tx := r.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, brand := range brands {
		if err := tx.Save(brand).Error; err != nil {
			tx.Rollback()
			return ecode.Wrap(err, ecode.ErrDatabase.Code, fmt.Sprintf("failed to update brand %d", brand.ID))
		}
	}

	if err := tx.Commit().Error; err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to commit batch update")
	}

	return nil
}

// GetBrandsByPlatform 根据平台类型获取商家列表
func (r *BrandRepositoryImpl) GetBrandsByPlatform(ctx *gin.Context, platformType string) ([]*entity.Brand, *ecode.Error) {
	var brands []*entity.Brand
	err := r.db.WithContext(ctx).Where("platform_type = ?", platformType).Find(&brands).Error
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get brands by platform")
	}
	return brands, nil
}

// FindBrandByPlatformAndMerchantID 根据平台类型和商家ID查找商家
func (r *BrandRepositoryImpl) FindBrandByPlatformAndMerchantID(ctx *gin.Context, platformType, merchantID string) (*entity.Brand, *ecode.Error) {
	var brand entity.Brand
	err := r.db.WithContext(ctx).Where("platform_type = ? AND platform_merchant_id = ?", platformType, merchantID).First(&brand).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to find brand by platform and merchant id")
	}
	return &brand, nil
}

// GetAllBrands 获取所有商家
func (r *BrandRepositoryImpl) GetAllBrands(ctx *gin.Context) ([]*entity.Brand, *ecode.Error) {
	var brands []*entity.Brand
	err := r.db.WithContext(ctx).Find(&brands).Error
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get all brands")
	}
	return brands, nil
}

// GetAllBrandsIncludingInactive 获取所有商家（包括已下线的）
func (r *BrandRepositoryImpl) GetAllBrandsIncludingInactive(ctx *gin.Context) ([]*entity.Brand, *ecode.Error) {
	var brands []*entity.Brand
	err := r.db.WithContext(ctx).Find(&brands).Error
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get all brands including inactive")
	}
	return brands, nil
}

// FindBrandsBySiteURL 根据site_url查找所有商家
func (r *BrandRepositoryImpl) FindBrandsBySiteURL(ctx *gin.Context, siteURL string) ([]*entity.Brand, *ecode.Error) {
	var brands []*entity.Brand
	err := r.db.WithContext(ctx).Where("site_url = ?", siteURL).Find(&brands).Error
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to find brands by site_url")
	}
	return brands, nil
}

// BatchUpdateBrandStatus 批量更新商家状态
func (r *BrandRepositoryImpl) BatchUpdateBrandStatus(ctx *gin.Context, brandIDs []uint, status int) *ecode.Error {
	if len(brandIDs) == 0 {
		return nil
	}

	result := r.db.WithContext(ctx).Model(&entity.Brand{}).Where("id IN ?", brandIDs).Update("status", status)
	if result.Error != nil {
		return ecode.Wrap(result.Error, ecode.ErrDatabase.Code, "failed to batch update brand status")
	}
	return nil
}

// UpdateBrandCouponCount 更新商家的优惠券数量
func (r *BrandRepositoryImpl) UpdateBrandCouponCount(ctx *gin.Context, brandID uint, count int) *ecode.Error {
	result := r.db.WithContext(ctx).Model(&entity.Brand{}).Where("id = ?", brandID).Update("total_coupons", count)
	if result.Error != nil {
		return ecode.Wrap(result.Error, ecode.ErrDatabase.Code, "failed to update brand coupon count")
	}
	if result.RowsAffected == 0 {
		return ecode.ErrNotFound
	}
	return nil
}

// UpdateBrandDealCount 更新商家的优惠活动数量
func (r *BrandRepositoryImpl) UpdateBrandDealCount(ctx *gin.Context, brandID uint, count int) *ecode.Error {
	result := r.db.WithContext(ctx).Model(&entity.Brand{}).Where("id = ?", brandID).Update("total_deals", count)
	if result.Error != nil {
		return ecode.Wrap(result.Error, ecode.ErrDatabase.Code, "failed to update brand deal count")
	}
	if result.RowsAffected == 0 {
		return ecode.ErrNotFound
	}
	return nil
}

// cleanBrandUTF8 cleans invalid UTF-8 characters from brand data
func cleanBrandUTF8(brand *entity.Brand) *entity.Brand {
	if brand == nil {
		return brand
	}
	
	// Clean all string fields
	brand.UniqueName = cleanUTF8String(brand.UniqueName)
	brand.Name = cleanUTF8String(brand.Name)
	brand.Description = cleanUTF8String(brand.Description)
	brand.Logo = cleanUTF8String(brand.Logo)
	brand.SiteURL = cleanUTF8String(brand.SiteURL)
	brand.OriginURL = cleanUTF8String(brand.OriginURL)
	brand.CountryName = cleanUTF8String(brand.CountryName)
	brand.TrackingURL = cleanUTF8String(brand.TrackingURL)
	brand.PlatformType = cleanUTF8String(brand.PlatformType)
	brand.PlatformMerchantID = cleanUTF8String(brand.PlatformMerchantID)
	
	// Clean SupportedCountries array
	for i, country := range brand.SupportedCountries {
		brand.SupportedCountries[i] = cleanUTF8String(country)
	}
	
	return brand
}
