package persistence

import (
	"strings"
	"unicode/utf8"
)

// shouldRetry determines if an error is retryable for PostgreSQL
func shouldRetry(err error) bool {
	if err == nil {
		return false
	}
	errStr := err.Error()
	// PostgreSQL-specific retryable errors
	// 40001: serialization_failure (deadlock-like issues)
	// 53300: too_many_connections
	// 08006: connection_failure
	// 22021: invalid_text_representation (UTF-8 encoding errors - not retryable)
	if strings.Contains(errStr, "22021") || strings.Contains(errStr, "invalid byte sequence for encoding") {
		return false // UTF-8 errors are not retryable, data needs cleaning
	}
	return strings.Contains(errStr, "40001") ||
		strings.Contains(errStr, "53300") ||
		strings.Contains(errStr, "08006") ||
		strings.Contains(errStr, "deadlock") ||
		strings.Contains(errStr, "connection")
}

// cleanUTF8String removes or replaces invalid UTF-8 sequences
func cleanUTF8String(s string) string {
	if s == "" {
		return s
	}
	
	// First check if string is already valid UTF-8
	if utf8.ValidString(s) {
		return s
	}
	
	// Convert to valid UTF-8 by replacing invalid sequences
	// This will replace invalid bytes with Unicode replacement character (\uFFFD)
	return strings.ToValidUTF8(s, "")
}