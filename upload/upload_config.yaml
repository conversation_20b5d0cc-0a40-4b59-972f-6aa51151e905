# SSH连接配置
ssh:
  host: "*************"
  user: "ro"
  password: "SzSv"
  port: 22
  frontend_path: "/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index"
  backend_paths:
    api: "/projects/bonusearned/bonusearned-api/"
    migrate: "/projects/bonusearned/bonusearned-migrate/"
    task: "/projects/bonusearned/bonusearned-task/"
    track: "/projects/bonusearned/bonusearned-track/"

# 本地文件路径配置
local:
  frontend_dist: "../../frontend-react/dist"
  backend_bin: "../../bin"
  backup_dir: "../../backups"

# 重试配置
retry:
  max_attempts: 5
  delay: "3s"

# 上传配置
upload:
  workers: 50      # 并发上传工作协程数
  buffer_size: 32   # 缓冲区大小(KB)