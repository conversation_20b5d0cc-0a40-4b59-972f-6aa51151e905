#!/usr/bin/env python3
"""
Deploy generated configuration files to the target frontend directory
"""

import os
import shutil
from pathlib import Path

def deploy_configs():
    """Deploy generated config files to the frontend directory"""
    print("🚀 Deploying configuration files...")
    
    # Source and target directories
    source_dir = Path(__file__).parent / "output"
    target_base = Path(__file__).parent.parent / "coupon-frontend" / "src" / "config"
    
    # File mappings
    file_mappings = {
        "variables.ts": target_base / "site" / "variables.ts",
        "branding.ts": target_base / "site" / "branding.ts", 
        "content.ts": target_base / "site" / "content.ts",
        "theme.ts": target_base / "theme" / "default.ts"
    }
    
    print(f"📁 Source directory: {source_dir}")
    print(f"📁 Target base: {target_base}")
    
    if not source_dir.exists():
        print(f"❌ Source directory does not exist: {source_dir}")
        return False
    
    deployed_count = 0
    
    for source_file, target_file in file_mappings.items():
        source_path = source_dir / source_file
        
        if source_path.exists():
            try:
                # Create target directory if it doesn't exist
                target_file.parent.mkdir(parents=True, exist_ok=True)
                
                # Copy file
                shutil.copy2(source_path, target_file)
                print(f"✅ Deployed: {source_file} -> {target_file.relative_to(target_base.parent.parent)}")
                deployed_count += 1
                
            except Exception as e:
                print(f"❌ Failed to deploy {source_file}: {e}")
        else:
            print(f"⚠️ Source file not found: {source_file}")
    
    print(f"\n📊 Deployment complete: {deployed_count}/{len(file_mappings)} files deployed")
    
    if deployed_count == len(file_mappings):
        print("🎉 All configuration files deployed successfully!")
        return True
    else:
        print("⚠️ Some files were not deployed. Please check the issues above.")
        return False

def verify_deployment():
    """Verify that deployed files are valid"""
    print("\n🔍 Verifying deployed files...")
    
    target_base = Path(__file__).parent.parent / "coupon-frontend" / "src" / "config"
    
    files_to_check = [
        target_base / "site" / "variables.ts",
        target_base / "site" / "branding.ts",
        target_base / "site" / "content.ts", 
        target_base / "theme" / "default.ts"
    ]
    
    verified_count = 0
    
    for file_path in files_to_check:
        if file_path.exists():
            try:
                # Basic validation - check if file is not empty and contains expected content
                content = file_path.read_text(encoding='utf-8')
                
                if len(content) > 100:  # Basic size check
                    if "export" in content:  # Basic TypeScript export check
                        print(f"✅ Valid: {file_path.name}")
                        verified_count += 1
                    else:
                        print(f"⚠️ Invalid format: {file_path.name} (no exports found)")
                else:
                    print(f"⚠️ Too small: {file_path.name} ({len(content)} characters)")
                    
            except Exception as e:
                print(f"❌ Error reading {file_path.name}: {e}")
        else:
            print(f"❌ Missing: {file_path.name}")
    
    print(f"\n📊 Verification complete: {verified_count}/{len(files_to_check)} files verified")
    return verified_count == len(files_to_check)

def main():
    """Main deployment function"""
    print("🎯 Frontend Config Deployment Tool")
    print("=" * 50)
    
    # Deploy files
    deploy_success = deploy_configs()
    
    if deploy_success:
        # Verify deployment
        verify_success = verify_deployment()
        
        if verify_success:
            print("\n🎉 Deployment and verification completed successfully!")
            print("\n📋 Next steps:")
            print("1. Check the deployed files in your frontend project")
            print("2. Run your frontend build to test the configurations")
            print("3. Make any necessary adjustments")
            return 0
        else:
            print("\n⚠️ Deployment completed but verification failed.")
            return 1
    else:
        print("\n❌ Deployment failed.")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
