#!/usr/bin/env python3
"""
Test script for the refactored frontend config generator
"""

import sys
import os
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_imports():
    """Test if all imports work correctly"""
    print("🧪 Testing imports...")
    
    try:
        # Test new structure imports
        from src.frontend_config_generator.core.config_loader import ConfigLoader
        print("✅ ConfigLoader import successful")
        
        from src.frontend_config_generator.core.main_generator import MainGenerator
        print("✅ MainGenerator import successful")
        
        from src.frontend_config_generator.generators.theme_generator import ThemeGenerator
        print("✅ ThemeGenerator import successful")
        
        from src.frontend_config_generator.utils.file_utils import FileUtils
        print("✅ FileUtils import successful")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_config_loader():
    """Test configuration loading"""
    print("\n🧪 Testing configuration loader...")
    
    try:
        from src.frontend_config_generator.core.config_loader import ConfigLoader
        
        # Test with default config
        config_loader = ConfigLoader()
        config = config_loader.get_config()
        
        print(f"✅ Config loaded successfully")
        print(f"   Site name: {config['site']['name']}")
        print(f"   Domain: {config['site']['domain']}")
        print(f"   AI model: {config['ai']['model']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Config loading failed: {e}")
        return False

def test_theme_generator():
    """Test theme generator with fallback"""
    print("\n🧪 Testing theme generator...")
    
    try:
        from src.frontend_config_generator.generators.theme_generator import ThemeGenerator
        
        theme_gen = ThemeGenerator()
        
        # Test fallback palette
        palette = theme_gen._get_fallback_palette()
        print(f"✅ Fallback palette generated with {len(palette)} color families")
        
        # Test complete fallback theme
        theme = theme_gen._get_complete_fallback_theme("TestSite")
        print(f"✅ Complete fallback theme generated")
        print(f"   Theme name: {theme['name']}")
        print(f"   Components: {len(theme['components'])}")
        print(f"   Gradients: {len(theme['gradients'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Theme generator test failed: {e}")
        return False

def test_file_generation():
    """Test file generation without AI"""
    print("\n🧪 Testing file generation...")
    
    try:
        from src.frontend_config_generator.core.main_generator import MainGenerator
        
        # Create generator
        generator = MainGenerator()
        
        # Test variables generation
        variables_content = generator.generate_variables_ts()
        print(f"✅ Variables.ts generated ({len(variables_content)} characters)")
        
        # Test branding generation
        branding_content = generator.generate_branding_ts()
        print(f"✅ Branding.ts generated ({len(branding_content)} characters)")
        
        # Test content generation
        content_content = generator.generate_content_ts()
        print(f"✅ Content.ts generated ({len(content_content)} characters)")
        
        # Test theme generation
        theme_content = generator.generate_theme_ts()
        print(f"✅ Theme.ts generated ({len(theme_content)} characters)")
        
        return True
        
    except Exception as e:
        print(f"❌ File generation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 Frontend Config Generator - Refactored Code Tests")
    print("=" * 60)
    
    tests = [
        ("Import Tests", test_imports),
        ("Config Loader Tests", test_config_loader),
        ("Theme Generator Tests", test_theme_generator),
        ("File Generation Tests", test_file_generation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} CRASHED: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The refactored code is working correctly.")
        return 0
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
