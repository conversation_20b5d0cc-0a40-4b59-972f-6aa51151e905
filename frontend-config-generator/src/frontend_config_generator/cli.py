#!/usr/bin/env python3
"""
Command line interface for Frontend Config Generator
"""

import sys
import argparse
from pathlib import Path
from typing import Optional

from .core.main_generator import MainGenerator
from .utils.validation_utils import ValidationUtils


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="AI-powered frontend configuration generator for coupon websites",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  frontend-config-gen                    # Use default config.yaml
  frontend-config-gen -c custom.yaml     # Use custom config file
  frontend-config-gen --validate-only    # Only validate config without generating
        """
    )
    
    parser.add_argument(
        "-c", "--config",
        type=str,
        help="Path to configuration YAML file (default: config.yaml)"
    )
    
    parser.add_argument(
        "--validate-only",
        action="store_true",
        help="Only validate configuration without generating files"
    )
    
    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="Enable verbose output"
    )
    
    parser.add_argument(
        "--version",
        action="version",
        version="Frontend Config Generator 2.0.0"
    )
    
    args = parser.parse_args()
    
    try:
        # Initialize generator
        if args.verbose:
            print("🚀 Initializing Frontend Config Generator...")
        
        generator = MainGenerator(config_path=args.config)
        
        if args.validate_only:
            print("✅ Configuration validation completed successfully!")
            return 0
        
        # Run generation
        generator.run()
        return 0
        
    except KeyboardInterrupt:
        print("\n❌ Generation interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


def validate_config(config_path: Optional[str] = None):
    """Validate configuration file"""
    try:
        from .core.config_loader import ConfigLoader
        
        config_loader = ConfigLoader(config_path)
        config = config_loader.get_config()
        
        # Perform detailed validation
        errors = ValidationUtils.validate_config_structure(config)
        
        if errors:
            print("❌ Configuration validation failed:")
            for section, error_list in errors.items():
                print(f"  {section}: {', '.join(error_list)}")
            return False
        else:
            print("✅ Configuration validation passed!")
            return True
            
    except Exception as e:
        print(f"❌ Validation error: {e}")
        return False


if __name__ == "__main__":
    sys.exit(main())
