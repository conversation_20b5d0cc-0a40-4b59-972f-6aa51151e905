#!/usr/bin/env python3
"""
Validation utilities for configuration validation
"""

import re
from typing import Dict, Any, List, Optional
from .color_utils import ColorUtils


class ValidationUtils:
    """
    Utility class for validation operations
    """

    @staticmethod
    def validate_domain(domain: str) -> bool:
        """
        Validate domain name format
        
        Args:
            domain: Domain name to validate
            
        Returns:
            True if valid domain, False otherwise
        """
        if not isinstance(domain, str) or not domain:
            return False
        
        # Basic domain validation regex
        domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
        return bool(re.match(domain_pattern, domain))

    @staticmethod
    def validate_url(url: str) -> bool:
        """
        Validate URL format
        
        Args:
            url: URL to validate
            
        Returns:
            True if valid URL, False otherwise
        """
        if not isinstance(url, str) or not url:
            return False
        
        # Basic URL validation regex
        url_pattern = r'^https?://[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*(/.*)?$'
        return bool(re.match(url_pattern, url))

    @staticmethod
    def validate_email(email: str) -> bool:
        """
        Validate email address format
        
        Args:
            email: Email address to validate
            
        Returns:
            True if valid email, False otherwise
        """
        if not isinstance(email, str) or not email:
            return False
        
        # Basic email validation regex
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(email_pattern, email))

    @staticmethod
    def validate_site_name(name: str) -> bool:
        """
        Validate site name
        
        Args:
            name: Site name to validate
            
        Returns:
            True if valid name, False otherwise
        """
        if not isinstance(name, str) or not name:
            return False
        
        # Site name should be 1-50 characters, alphanumeric and spaces
        return 1 <= len(name.strip()) <= 50 and bool(re.match(r'^[a-zA-Z0-9\s]+$', name.strip()))

    @staticmethod
    def validate_color_config(colors: Dict[str, Any]) -> bool:
        """
        Validate color configuration
        
        Args:
            colors: Color configuration dictionary
            
        Returns:
            True if valid color config, False otherwise
        """
        if not isinstance(colors, dict):
            return False
        
        required_colors = ['primary', 'secondary', 'accent']
        
        for color_name in required_colors:
            if color_name not in colors:
                return False
            
            color_value = colors[color_name]
            if isinstance(color_value, str):
                # Simple hex color
                if not ColorUtils.is_valid_hex_color(color_value):
                    return False
            elif isinstance(color_value, dict):
                # Color palette with levels
                for level, hex_color in color_value.items():
                    if not ColorUtils.is_valid_hex_color(hex_color):
                        return False
            else:
                return False
        
        return True

    @staticmethod
    def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> List[str]:
        """
        Validate that required fields are present in data
        
        Args:
            data: Data dictionary to validate
            required_fields: List of required field names
            
        Returns:
            List of missing field names
        """
        if not isinstance(data, dict):
            return required_fields.copy()
        
        missing_fields = []
        for field in required_fields:
            if field not in data or data[field] is None:
                missing_fields.append(field)
        
        return missing_fields

    @staticmethod
    def validate_config_structure(config: Dict[str, Any]) -> Dict[str, List[str]]:
        """
        Validate complete configuration structure
        
        Args:
            config: Configuration dictionary to validate
            
        Returns:
            Dictionary with validation errors by section
        """
        errors = {}
        
        # Validate top-level structure
        required_top_level = ['site', 'ai', 'output', 'generated_files']
        missing_top_level = ValidationUtils.validate_required_fields(config, required_top_level)
        if missing_top_level:
            errors['top_level'] = missing_top_level
        
        # Validate site configuration
        if 'site' in config:
            site_config = config['site']
            required_site = ['name', 'domain', 'api_url']
            missing_site = ValidationUtils.validate_required_fields(site_config, required_site)
            if missing_site:
                errors['site'] = missing_site
            
            # Validate specific site fields
            site_errors = []
            if 'name' in site_config and not ValidationUtils.validate_site_name(site_config['name']):
                site_errors.append('Invalid site name format')
            if 'domain' in site_config and not ValidationUtils.validate_domain(site_config['domain']):
                site_errors.append('Invalid domain format')
            if 'api_url' in site_config and not ValidationUtils.validate_url(site_config['api_url']):
                site_errors.append('Invalid API URL format')
            if 'contact_email' in site_config and not ValidationUtils.validate_email(site_config['contact_email']):
                site_errors.append('Invalid contact email format')
            
            if site_errors:
                errors['site_validation'] = site_errors
        
        # Validate AI configuration
        if 'ai' in config:
            ai_config = config['ai']
            required_ai = ['model']
            missing_ai = ValidationUtils.validate_required_fields(ai_config, required_ai)
            if missing_ai:
                errors['ai'] = missing_ai
        
        # Validate output configuration
        if 'output' in config:
            output_config = config['output']
            required_output = ['directory']
            missing_output = ValidationUtils.validate_required_fields(output_config, required_output)
            if missing_output:
                errors['output'] = missing_output
        
        return errors
