#!/usr/bin/env python3
"""
File utilities for handling file operations
"""

import os
from pathlib import Path
from typing import Union


class FileUtils:
    """
    Utility class for file operations
    """

    def __init__(self, output_dir: Union[str, Path]):
        """
        Initialize file utilities
        
        Args:
            output_dir: Output directory for generated files
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

    def write_file(self, filename: str, content: str) -> bool:
        """
        Write content to specified output file
        
        Args:
            filename: Name of the file to write
            content: Content to write to the file
            
        Returns:
            True if successful, False otherwise
        """
        file_path = self.output_dir / filename
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ File generated: {file_path}")
            return True
        except IOError as e:
            print(f"❌ Error: Unable to write file {filename}: {e}")
            return False

    def read_file(self, filename: str) -> str:
        """
        Read content from file
        
        Args:
            filename: Name of the file to read
            
        Returns:
            File content as string
        """
        file_path = self.output_dir / filename
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except IOError as e:
            print(f"❌ Error: Unable to read file {filename}: {e}")
            return ""

    def file_exists(self, filename: str) -> bool:
        """
        Check if file exists in output directory
        
        Args:
            filename: Name of the file to check
            
        Returns:
            True if file exists, False otherwise
        """
        file_path = self.output_dir / filename
        return file_path.exists()

    def delete_file(self, filename: str) -> bool:
        """
        Delete file from output directory
        
        Args:
            filename: Name of the file to delete
            
        Returns:
            True if successful, False otherwise
        """
        file_path = self.output_dir / filename
        try:
            if file_path.exists():
                file_path.unlink()
                print(f"🗑️ File deleted: {file_path}")
                return True
            else:
                print(f"⚠️ File not found: {file_path}")
                return False
        except OSError as e:
            print(f"❌ Error: Unable to delete file {filename}: {e}")
            return False

    def list_files(self) -> list:
        """
        List all files in output directory
        
        Returns:
            List of filenames
        """
        try:
            return [f.name for f in self.output_dir.iterdir() if f.is_file()]
        except OSError as e:
            print(f"❌ Error: Unable to list files: {e}")
            return []

    def get_output_dir(self) -> Path:
        """
        Get the output directory path
        
        Returns:
            Path object of output directory
        """
        return self.output_dir
