#!/usr/bin/env python3
"""
Color utilities for color manipulation and validation
"""

import re
import colorsys
from typing import Tuple, Optional


class ColorUtils:
    """
    Utility class for color operations
    """

    @staticmethod
    def hex_to_rgb(hex_color: str) -> Optional[Tuple[int, int, int]]:
        """
        Convert hex color to RGB tuple
        
        Args:
            hex_color: Hex color string (e.g., "#ff0000" or "ff0000")
            
        Returns:
            RGB tuple (r, g, b) or None if invalid
        """
        # Remove # if present
        hex_color = hex_color.lstrip('#')
        
        # Validate hex color format
        if not re.match(r'^[0-9a-fA-F]{6}$', hex_color):
            return None
        
        try:
            return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        except ValueError:
            return None

    @staticmethod
    def rgb_to_hex(r: int, g: int, b: int) -> str:
        """
        Convert RGB values to hex color
        
        Args:
            r: Red value (0-255)
            g: Green value (0-255)
            b: Blue value (0-255)
            
        Returns:
            Hex color string
        """
        return f"#{r:02x}{g:02x}{b:02x}"

    @staticmethod
    def is_valid_hex_color(color: str) -> bool:
        """
        Validate if string is a valid hex color
        
        Args:
            color: Color string to validate
            
        Returns:
            True if valid hex color, False otherwise
        """
        if not isinstance(color, str):
            return False
        
        # Remove # if present
        color = color.lstrip('#')
        
        # Check if it's a valid 6-digit hex color
        return bool(re.match(r'^[0-9a-fA-F]{6}$', color))

    @staticmethod
    def lighten_color(hex_color: str, factor: float = 0.1) -> str:
        """
        Lighten a hex color by a given factor
        
        Args:
            hex_color: Hex color string
            factor: Lightening factor (0.0 to 1.0)
            
        Returns:
            Lightened hex color string
        """
        rgb = ColorUtils.hex_to_rgb(hex_color)
        if not rgb:
            return hex_color
        
        # Convert to HSL, increase lightness, convert back
        h, l, s = colorsys.rgb_to_hls(rgb[0]/255, rgb[1]/255, rgb[2]/255)
        l = min(1.0, l + factor)
        r, g, b = colorsys.hls_to_rgb(h, l, s)
        
        return ColorUtils.rgb_to_hex(int(r*255), int(g*255), int(b*255))

    @staticmethod
    def darken_color(hex_color: str, factor: float = 0.1) -> str:
        """
        Darken a hex color by a given factor
        
        Args:
            hex_color: Hex color string
            factor: Darkening factor (0.0 to 1.0)
            
        Returns:
            Darkened hex color string
        """
        rgb = ColorUtils.hex_to_rgb(hex_color)
        if not rgb:
            return hex_color
        
        # Convert to HSL, decrease lightness, convert back
        h, l, s = colorsys.rgb_to_hls(rgb[0]/255, rgb[1]/255, rgb[2]/255)
        l = max(0.0, l - factor)
        r, g, b = colorsys.hls_to_rgb(h, l, s)
        
        return ColorUtils.rgb_to_hex(int(r*255), int(g*255), int(b*255))

    @staticmethod
    def get_contrast_ratio(color1: str, color2: str) -> float:
        """
        Calculate contrast ratio between two colors
        
        Args:
            color1: First hex color
            color2: Second hex color
            
        Returns:
            Contrast ratio (1.0 to 21.0)
        """
        def get_luminance(hex_color: str) -> float:
            rgb = ColorUtils.hex_to_rgb(hex_color)
            if not rgb:
                return 0.0
            
            # Convert to relative luminance
            def linearize(c):
                c = c / 255.0
                return c / 12.92 if c <= 0.03928 else ((c + 0.055) / 1.055) ** 2.4
            
            r, g, b = [linearize(c) for c in rgb]
            return 0.2126 * r + 0.7152 * g + 0.0722 * b
        
        lum1 = get_luminance(color1)
        lum2 = get_luminance(color2)
        
        # Ensure lighter color is in numerator
        if lum1 < lum2:
            lum1, lum2 = lum2, lum1
        
        return (lum1 + 0.05) / (lum2 + 0.05)

    @staticmethod
    def is_accessible_contrast(foreground: str, background: str, level: str = "AA") -> bool:
        """
        Check if color combination meets WCAG accessibility standards
        
        Args:
            foreground: Foreground hex color
            background: Background hex color
            level: WCAG level ("AA" or "AAA")
            
        Returns:
            True if accessible, False otherwise
        """
        ratio = ColorUtils.get_contrast_ratio(foreground, background)
        
        if level == "AAA":
            return ratio >= 7.0
        else:  # AA level
            return ratio >= 4.5
