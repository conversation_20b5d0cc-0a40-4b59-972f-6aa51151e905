#!/usr/bin/env python3
"""
Content generator module
Specialized for generating website content configurations
"""

import sys
import os
from typing import Dict, Any

# Add the project root to Python path for imports
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
sys.path.append(project_root)

try:
    from external_gateway.g4flib.g4fclient import send_message_with_validation
except ImportError as e:
    print(f"⚠️ Import warning: {e}")
    send_message_with_validation = None


class ContentGenerator:
    """Content configuration generator"""
    
    def __init__(self, gpt_model: str = "gpt_4o", proxy: str = None):
        """
        Initialize content generator
        
        Args:
            gpt_model: GPT model name
            proxy: Proxy settings
        """
        self.gpt_model = gpt_model
        self.proxy = proxy
        
    def _send_prompt(self, prompt: str, required_schema: list = None) -> Dict[str, Any]:
        """Send prompt to GPT"""
        try:
            if not send_message_with_validation:
                return self._get_fallback_content()
                
            result = send_message_with_validation(
                prompt, 
                self.gpt_model, 
                self.proxy,
                required_schema=required_schema,
                max_retries=3
            )
            
            return result if result and isinstance(result, dict) else self._get_fallback_content()
                
        except Exception as e:
            print(f"Error: GPT call failed: {e}")
            return self._get_fallback_content()

    def _get_fallback_content(self) -> Dict[str, Any]:
        """Get fallback content when AI is not available"""
        return {
            "hero": {
                "searchPlaceholder": "Find coupons for brands, stores, and deals you want"
            },
            "sections": {
                "categories": "Browse Categories",
                "brands": "Top Brands",
                "featuredCoupons": "Exclusive Coupons",
                "featuredDeals": "Limited Time Deals"
            },
            "buttons": {
                "search": "Find Deals",
                "viewAll": "View All Deals",
                "getCoupon": "Get Code",
                "getDeal": "Get Deal",
                "subscribe": "Subscribe",
                "unsubscribe": "Unsubscribe"
            },
            "messages": {
                "loading": "Fetching your coupons, please wait!",
                "noResults": "Oops! No coupons match your search criteria.",
                "error": "Sorry, something went wrong. Please try again.",
                "success": "Yay! Your subscription was successful!"
            },
            "newsletter": {
                "title": "Unlock VIP Savings Early! 🚨",
                "description": "Be first to grab premium coupons & secret sales!",
                "placeholder": "Unlock savings with your email"
            }
        }

    def generate_content_config(self, domain: str, site_name: str, api_url: str) -> Dict[str, Any]:
        """Generate complete content configuration"""
        prompt = f"""You are a professional copywriter specializing in e-commerce and coupon websites. 
Generate engaging content for "{site_name}" coupon website (domain: {domain}).

Create content that is:
1. Engaging and action-oriented
2. Clear and user-friendly
3. Optimized for conversions
4. Consistent with coupon/deals theme

Generate JSON with these sections:
{{
  "hero": {{
    "searchPlaceholder": "Search placeholder text"
  }},
  "sections": {{
    "categories": "Categories section title",
    "brands": "Brands section title", 
    "featuredCoupons": "Featured coupons title",
    "featuredDeals": "Featured deals title"
  }},
  "buttons": {{
    "search": "Search button text",
    "viewAll": "View all button text",
    "getCoupon": "Get coupon button text",
    "getDeal": "Get deal button text",
    "subscribe": "Subscribe button text",
    "unsubscribe": "Unsubscribe button text"
  }},
  "messages": {{
    "loading": "Loading message",
    "noResults": "No results message",
    "error": "Error message",
    "success": "Success message"
  }},
  "newsletter": {{
    "title": "Newsletter signup title",
    "description": "Newsletter description",
    "placeholder": "Email input placeholder"
  }}
}}"""

        return self._send_prompt(prompt, ["hero", "sections", "buttons", "messages", "newsletter"])
