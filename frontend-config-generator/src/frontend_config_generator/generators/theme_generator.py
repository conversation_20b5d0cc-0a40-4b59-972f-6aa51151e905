#!/usr/bin/env python3
"""
Advanced theme generator module
Specialized for detailed UI component color design, supports batch GPT calls and algorithm optimization
"""

import json
import colorsys
import random
import sys
import os
from typing import Dict, Any, List, Tuple

# Add the project root to Python path for imports
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
sys.path.append(project_root)

try:
    from external_gateway.g4flib.g4fclient import send_message_with_validation
except ImportError as e:
    print(f"⚠️ Import warning: {e}")
    send_message_with_validation = None


class ThemeGenerator:
    """Advanced theme color generator"""
    
    def __init__(self, gpt_model: str = "gpt_4o", proxy: str = None):
        """
        Initialize generator
        
        Args:
            gpt_model: GPT model name
            proxy: Proxy settings
        """
        self.gpt_model = gpt_model
        self.proxy = proxy
        
    def _send_prompt(self, prompt: str, required_schema: list = None) -> Dict[str, Any]:
        """Send prompt to GPT"""
        try:
            if not send_message_with_validation:
                return {}
                
            result = send_message_with_validation(
                prompt, 
                self.gpt_model, 
                self.proxy,
                required_schema=required_schema,
                max_retries=3,
                color_validation=True
            )
            
            return result if result and isinstance(result, dict) else {}
                
        except Exception as e:
            print(f"Error: GPT call failed: {e}")
            return {}
    
    def _generate_color_palette(self, brand_name: str) -> Dict[str, Any]:
        """Generate basic color palette"""
        prompt = f"""You are a professional UI/UX color designer. Generate an advanced color scheme for "{brand_name}" coupon website.

Task: Generate a complete color palette, each color includes 11 levels (50-950).

Requirements:
1. Generate 8 color families: primary, secondary, accent, success, warning, error, info, neutral
2. Each color family must have 11 levels: 50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950
3. Colors must be professional, accessible, and suitable for coupon/e-commerce websites
4. Use hex color codes only
5. Ensure proper contrast ratios

Return JSON format:
{{
  "primary": {{
    "50": "#f0fdf4",
    "100": "#dcfce7",
    ...
    "950": "#052e16"
  }},
  "secondary": {{ ... }},
  "accent": {{ ... }},
  "success": {{ ... }},
  "warning": {{ ... }},
  "error": {{ ... }},
  "info": {{ ... }},
  "neutral": {{ ... }}
}}"""

        result = self._send_prompt(prompt, ["primary", "secondary", "accent", "success", "warning", "error", "info", "neutral"])
        
        if not result:
            # Fallback color palette
            return self._get_fallback_palette()
        
        return result
    
    def _get_fallback_palette(self) -> Dict[str, Any]:
        """Get fallback color palette when AI is not available"""
        return {
            "primary": {
                "50": "#f0fdf4", "100": "#dcfce7", "200": "#bbf7d0", "300": "#86efac", "400": "#4ade80",
                "500": "#22c55e", "600": "#16a34a", "700": "#15803d", "800": "#166534", "900": "#14532d", "950": "#052e16"
            },
            "secondary": {
                "50": "#eff6ff", "100": "#dbeafe", "200": "#bfdbfe", "300": "#93c5fd", "400": "#60a5fa",
                "500": "#3b82f6", "600": "#2563eb", "700": "#1d4ed8", "800": "#1e40af", "900": "#1e3a8a", "950": "#172554"
            },
            "accent": {
                "50": "#faf5ff", "100": "#f3e8ff", "200": "#e9d5ff", "300": "#d8b4fe", "400": "#c084fc",
                "500": "#a855f7", "600": "#9333ea", "700": "#7c3aed", "800": "#6b21a8", "900": "#581c87", "950": "#3b0764"
            },
            "success": {
                "50": "#f0fdf4", "100": "#dcfce7", "200": "#bbf7d0", "300": "#86efac", "400": "#4ade80",
                "500": "#22c55e", "600": "#16a34a", "700": "#15803d", "800": "#166534", "900": "#14532d", "950": "#052e16"
            },
            "warning": {
                "50": "#fefce8", "100": "#fef9c3", "200": "#fef08a", "300": "#fde047", "400": "#facc15",
                "500": "#eab308", "600": "#ca8a04", "700": "#a16207", "800": "#854d0e", "900": "#713f12", "950": "#422006"
            },
            "error": {
                "50": "#fef2f2", "100": "#fee2e2", "200": "#fecaca", "300": "#fca5a5", "400": "#f87171",
                "500": "#ef4444", "600": "#dc2626", "700": "#b91c1c", "800": "#991b1b", "900": "#7f1d1d", "950": "#450a0a"
            },
            "info": {
                "50": "#eff6ff", "100": "#dbeafe", "200": "#bfdbfe", "300": "#93c5fd", "400": "#60a5fa",
                "500": "#3b82f6", "600": "#2563eb", "700": "#1d4ed8", "800": "#1e40af", "900": "#1e3a8a", "950": "#172554"
            },
            "neutral": {
                "50": "#fafafa", "100": "#f5f5f5", "200": "#e5e5e5", "300": "#d4d4d4", "400": "#a3a3a3",
                "500": "#737373", "600": "#525252", "700": "#404040", "800": "#262626", "900": "#171717", "950": "#0a0a0a"
            }
        }

    def generate_complete_theme(self, site_name: str, domain: str, api_url: str) -> Dict[str, Any]:
        """Generate complete theme configuration"""
        print(f"🎨 Generating complete theme for {site_name}...")

        try:
            # Generate color palette
            colors = self._generate_color_palette(site_name)

            if not colors:
                print("❌ Failed to generate color palette, using fallback")
                colors = self._get_fallback_palette()

            # Generate component colors with error handling
            components = {}
            try:
                components = self._generate_component_colors(colors)
                print(f"✅ Generated {len(components)} component styles")
            except Exception as e:
                print(f"⚠️ Component generation failed: {e}, using fallback")
                components = self._get_fallback_components()

            # Generate gradients with error handling
            gradients = {}
            try:
                gradients = self._generate_gradients(colors)
                print(f"✅ Generated {len(gradients)} gradient styles")
            except Exception as e:
                print(f"⚠️ Gradient generation failed: {e}, using fallback")
                gradients = self._get_fallback_gradients()

            theme_config = {
                "name": f"{site_name} Default Theme",
                "description": f"Default theme for {site_name} website",
                "version": "1.0.0",
                "colors": colors,
                "components": components,
                "gradients": gradients
            }

            print(f"✅ Theme generated successfully!")
            return theme_config

        except Exception as e:
            print(f"❌ Error generating complete theme: {e}")
            print("🔄 Using complete fallback theme")
            return self._get_complete_fallback_theme(site_name)

    def _generate_component_colors(self, colors: Dict) -> Dict[str, Any]:
        """Generate component-specific colors"""
        # This is a simplified version - in a full implementation, 
        # this would use AI to generate detailed component colors
        primary = colors.get("primary", {})
        secondary = colors.get("secondary", {})
        neutral = colors.get("neutral", {})
        
        return {
            "brandCard": {
                "container": {
                    "background": "#ffffff",
                    "border": neutral.get("200", "#e5e5e5"),
                    "shadow": "0 2px 8px rgba(0, 0, 0, 0.1)",
                    "hover": {
                        "background": neutral.get("50", "#fafafa"),
                        "border": primary.get("600", "#16a34a"),
                        "shadow": f"0 4px 16px rgba(34, 197, 94, 0.2)"
                    }
                }
            },
            "couponCard": {
                "container": {
                    "background": "#ffffff",
                    "border": neutral.get("200", "#e5e5e5"),
                    "shadow": "0 2px 8px rgba(0, 0, 0, 0.08)"
                }
            }
        }

    def _generate_gradients(self, colors: Dict) -> Dict[str, Any]:
        """Generate gradient configurations"""
        primary = colors.get("primary", {})
        return {
            "hero": "linear-gradient(to bottom right, #dbeafe, #dcfce7, #fef9c3)",
            "card": "linear-gradient(to bottom right, transparent, #f9fafb, #f3f4f6)",
            "button": f"linear-gradient(to right, {primary.get('600', '#16a34a')}, {primary.get('700', '#15803d')})",
            "discount": "linear-gradient(to right, #dc2626, #b91c1c)",
            "hover": {
                "primary": f"linear-gradient(to bottom right, #dcfce7, #bbf7d0, #86efac)",
                "secondary": f"linear-gradient(to bottom right, #dbeafe, #bfdbfe, #93c5fd)",
                "accent": f"linear-gradient(to bottom right, #f3e8ff, #e9d5ff, #d8b4fe)"
            }
        }

    def _get_fallback_components(self) -> Dict[str, Any]:
        """Get fallback component colors when AI generation fails"""
        return {
            "brandCard": {
                "container": {
                    "background": "#ffffff",
                    "border": "#e5e7eb",
                    "shadow": "0 2px 8px rgba(0, 0, 0, 0.1)",
                    "hover": {
                        "background": "#fafafa",
                        "border": "#16a34a",
                        "shadow": "0 4px 16px rgba(34, 197, 94, 0.2)"
                    }
                },
                "brandName": {
                    "text": "#16a34a",
                    "border": "#dcfce7",
                    "background": "rgba(220, 252, 231, 0.5)"
                }
            },
            "couponCard": {
                "container": {
                    "background": "#ffffff",
                    "border": "#e5e7eb",
                    "shadow": "0 2px 8px rgba(0, 0, 0, 0.08)"
                },
                "button": {
                    "background": "#16a34a",
                    "text": "#ffffff",
                    "border": "transparent",
                    "hover": "#15803d"
                }
            }
        }

    def _get_fallback_gradients(self) -> Dict[str, Any]:
        """Get fallback gradients when generation fails"""
        return {
            "hero": "linear-gradient(to bottom right, #dbeafe, #dcfce7, #fef9c3)",
            "card": "linear-gradient(to bottom right, transparent, #f9fafb, #f3f4f6)",
            "button": "linear-gradient(to right, #16a34a, #15803d)",
            "discount": "linear-gradient(to right, #dc2626, #b91c1c)"
        }

    def _get_complete_fallback_theme(self, site_name: str) -> Dict[str, Any]:
        """Get complete fallback theme when all generation fails"""
        return {
            "name": f"{site_name} Fallback Theme",
            "description": f"Fallback theme for {site_name} website",
            "version": "1.0.0",
            "colors": self._get_fallback_palette(),
            "components": self._get_fallback_components(),
            "gradients": self._get_fallback_gradients()
        }
