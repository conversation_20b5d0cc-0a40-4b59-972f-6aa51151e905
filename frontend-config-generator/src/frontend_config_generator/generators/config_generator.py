#!/usr/bin/env python3
"""
Configuration generator module - refactored version
Based on g4fclient for AI-powered configuration generation
"""

import json
import sys
import os
from typing import Dict, Any, Optional

# Add the project root to Python path for imports
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
sys.path.append(project_root)

try:
    from prompts import PromptTemplates
    from external_gateway.g4flib.g4fclient import send_message_with_validation
    from hybrid_color_generator import HybridColorGenerator
except ImportError as e:
    print(f"⚠️ Import warning: {e}")
    print("🔄 Falling back to basic functionality...")
    PromptTemplates = None
    send_message_with_validation = None
    HybridColorGenerator = None


class ConfigGenerator:
    """Frontend configuration generator based on g4fclient"""
    
    def __init__(self, gpt_model: str = "gpt_4o", proxy: Optional[str] = None):
        """
        Initialize configuration generator
        
        Args:
            gpt_model: GPT model name, defaults to gpt_4o
            proxy: Proxy settings, optional
        """
        self.gpt_model = gpt_model
        self.proxy = proxy
        
        # Initialize hybrid color generator (mathematical algorithm + GPT intelligent optimization)
        if HybridColorGenerator:
            self.hybrid_generator = HybridColorGenerator()
        else:
            self.hybrid_generator = None
    
    def _send_prompt(self, prompt: str, required_schema: list = None, color_validation: bool = False) -> Dict[str, Any]:
        """
        Send prompt to GPT and parse result with JSON validation
        
        Args:
            prompt: Prompt text
            required_schema: Required JSON field list
            color_validation: Whether to perform deep validation of color data
            
        Returns:
            Parsed data dictionary
        """
        try:
            if not send_message_with_validation:
                print("⚠️ AI service not available, using fallback data")
                return self._get_fallback_data()

            result = send_message_with_validation(
                prompt, 
                self.gpt_model, 
                self.proxy,
                required_schema=required_schema,
                max_retries=3,
                color_validation=color_validation
            )
            
            return result if result and isinstance(result, dict) else self._get_fallback_data()
                
        except Exception as e:
            print(f"Error: GPT call failed: {e}")
            return self._get_fallback_data()

    def _get_fallback_data(self) -> Dict[str, Any]:
        """Get fallback data when AI is not available"""
        return {
            "tagline": "Discover Amazing Deals",
            "description": "Find the best coupons and deals from top brands",
            "VERIFIED_COUPONS": "Verified Coupons",
            "EXCLUSIVE_DEALS": "Exclusive Deals", 
            "PRICE_ALERTS": "Price Alerts",
            "CASHBACK": "Cashback Offers",
            "icon": "🎫",
            "iconBg": "bg-gradient-to-br from-green-400 to-blue-500",
            "colors": {
                "primary": "#10b981",
                "secondary": "#3b82f6", 
                "accent": "#8b5cf6"
            }
        }

    def generate_tagline_and_description(self, domain: str, site_name: str) -> Dict[str, Any]:
        """Generate tagline and description for the site"""
        if not PromptTemplates:
            return self._get_fallback_data()
            
        try:
            prompt = PromptTemplates.get_tagline_prompt(domain, site_name)
            return self._send_prompt(prompt, ["tagline", "description"])
        except Exception as e:
            print(f"Error generating tagline: {e}")
            return self._get_fallback_data()

    def generate_features_config(self, domain: str, site_name: str) -> Dict[str, Any]:
        """Generate features configuration"""
        if not PromptTemplates:
            return self._get_fallback_data()
            
        try:
            prompt = PromptTemplates.get_features_prompt(domain, site_name)
            return self._send_prompt(prompt, ["VERIFIED_COUPONS", "EXCLUSIVE_DEALS", "PRICE_ALERTS", "CASHBACK"])
        except Exception as e:
            print(f"Error generating features: {e}")
            return self._get_fallback_data()

    def generate_branding_content(self, domain: str, site_name: str, api_url: str) -> Dict[str, Any]:
        """Generate branding content"""
        if not PromptTemplates:
            return self._get_fallback_data()
            
        try:
            prompt = PromptTemplates.get_branding_prompt(domain, site_name, api_url)
            return self._send_prompt(prompt, ["icon", "iconBg", "colors"])
        except Exception as e:
            print(f"Error generating branding: {e}")
            return self._get_fallback_data()

    def generate_content_config(self, domain: str, site_name: str, api_url: str) -> Dict[str, Any]:
        """Generate content configuration"""
        if not PromptTemplates:
            return self._get_fallback_data()
            
        try:
            prompt = PromptTemplates.get_content_prompt(domain, site_name, api_url)
            return self._send_prompt(prompt, ["hero", "sections", "buttons", "messages", "newsletter"])
        except Exception as e:
            print(f"Error generating content: {e}")
            return self._get_fallback_data()

    def generate_theme_colors(self, domain: str, site_name: str, style: str = "professional") -> Dict[str, Any]:
        """Generate theme colors"""
        if self.hybrid_generator:
            try:
                return self.hybrid_generator.generate_color_palette(site_name, style)
            except Exception as e:
                print(f"Error with hybrid generator: {e}")
        
        return self._get_fallback_data().get("colors", {})

    def generate_theme_specific_colors(self, base_colors: Dict, domain: str, site_name: str) -> Dict[str, Any]:
        """Generate specific component colors"""
        if not PromptTemplates:
            return {}
            
        try:
            prompt = PromptTemplates.get_specific_colors_prompt(base_colors, domain, site_name)
            return self._send_prompt(prompt, color_validation=True)
        except Exception as e:
            print(f"Error generating specific colors: {e}")
            return {}

    def generate_theme_gradients(self, colors: Dict, specific_colors: Dict) -> Dict[str, Any]:
        """Generate gradient configurations"""
        if not PromptTemplates:
            return {}
            
        try:
            prompt = PromptTemplates.get_gradients_prompt(colors, specific_colors)
            return self._send_prompt(prompt, color_validation=True)
        except Exception as e:
            print(f"Error generating gradients: {e}")
            return {}
