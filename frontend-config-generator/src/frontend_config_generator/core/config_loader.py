#!/usr/bin/env python3
"""
Configuration loader module
Handles loading and validation of YAML configuration files
"""

import os
import sys
import yaml
from typing import Dict, Any, Optional
from pathlib import Path


class ConfigLoader:
    """
    Configuration loader and validator
    """

    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize config loader
        
        Args:
            config_path: Path to config file, defaults to config.yaml in project root
        """
        if config_path is None:
            # Find config.yaml in project root
            project_root = self._find_project_root()
            config_path = project_root / "config.yaml"
        
        self.config_path = Path(config_path)
        self.config = self._load_config()
        self._validate_config()

    def _find_project_root(self) -> Path:
        """Find the project root directory"""
        current = Path(__file__).parent
        while current.parent != current:
            if (current / "config.yaml").exists() or (current / "setup.py").exists():
                return current
            current = current.parent
        
        # Fallback to current working directory
        return Path.cwd()

    def _load_config(self) -> Dict[str, Any]:
        """Load and parse YAML configuration file"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            print(f"❌ Error: Configuration file '{self.config_path}' not found.")
            sys.exit(1)
        except yaml.YAMLError as e:
            print(f"❌ Error: Failed to parse YAML file: {e}")
            sys.exit(1)

    def _validate_config(self):
        """Validate that the loaded config contains required fields"""
        required_keys = ['site', 'ai', 'output', 'generated_files']
        for key in required_keys:
            if key not in self.config:
                print(f"❌ Error: Missing required top-level key in config: '{key}'")
                sys.exit(1)

        required_site_keys = ['name', 'domain', 'api_url']
        for key in required_site_keys:
            if key not in self.config['site']:
                print(f"❌ Error: Missing required key in 'site' config: '{key}'")
                sys.exit(1)

    def get_config(self) -> Dict[str, Any]:
        """Get the loaded configuration"""
        return self.config

    def get_site_config(self) -> Dict[str, Any]:
        """Get site-specific configuration"""
        return self.config['site']

    def get_ai_config(self) -> Dict[str, Any]:
        """Get AI-specific configuration"""
        return self.config['ai']

    def get_output_config(self) -> Dict[str, Any]:
        """Get output configuration"""
        return self.config['output']

    def get_generated_files(self) -> list:
        """Get list of files to generate"""
        return self.config['generated_files']
