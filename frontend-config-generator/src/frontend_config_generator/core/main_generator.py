#!/usr/bin/env python3
"""
Main generator module - refactored version
Coordinates the entire configuration file generation process
"""

import os
import sys
from typing import Dict, Any, Optional
from pathlib import Path

from .config_loader import ConfigLoader
from ..generators.config_generator import ConfigGenerator
from ..utils.file_utils import FileUtils


class MainGenerator:
    """
    Main generator class responsible for coordinating the entire configuration file generation process
    """

    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the generator
        
        Args:
            config_path: Path to YAML configuration file
        """
        # Load configuration
        self.config_loader = ConfigLoader(config_path)
        self.config = self.config_loader.get_config()
        
        # Setup output directory
        self.output_dir = Path(self.config['output']['directory'])
        self.output_dir.mkdir(exist_ok=True)
        
        # Initialize file utilities
        self.file_utils = FileUtils(self.output_dir)
        
        # Initialize config generator
        ai_config = self.config_loader.get_ai_config()
        self.config_generator = ConfigGenerator(
            gpt_model=ai_config['model'],
            proxy=ai_config.get('proxy')
        )
        
        # Site information
        self.site_info = self.config_loader.get_site_config()
        self.site_name = self.site_info['name']
        self.domain = self.site_info['domain']
        self.api_url = self.site_info['api_url']
        
        # Generation summary
        self.summary = {}

    def generate_variables_ts(self) -> str:
        """Generate variables.ts file content"""
        print("⏳ Generating variables.ts...")
        
        try:
            tagline_desc_result = self.config_generator.generate_tagline_and_description(
                self.domain, self.site_name
            )
            features_result = self.config_generator.generate_features_config(
                self.domain, self.site_name
            )
            
            api_domain = self.api_url.replace('https://', '').replace('http://', '').split('/')[0]
            
            # Set summary based on actual results
            summary_data = {}
            summary_data["tagline_desc"] = "AI" if tagline_desc_result and tagline_desc_result.get("tagline") else "Fallback"
            summary_data["features"] = "AI" if features_result and all(k in features_result for k in
                                                                       ["VERIFIED_COUPONS", "EXCLUSIVE_DEALS",
                                                                        "PRICE_ALERTS", "CASHBACK"]) else "Fallback"
            self.summary["variables.ts"] = summary_data
            
            content = self._build_variables_content(tagline_desc_result, features_result, api_domain)
            print("👍 variables.ts content generated successfully.")
            return content
            
        except Exception as e:
            print(f"❌ Error generating variables.ts: {e}")
            self.summary["variables.ts"] = {"error": str(e)}
            raise

    def _build_variables_content(self, tagline_desc: Dict, features: Dict, api_domain: str) -> str:
        """Build the variables.ts content string"""
        return f"""// Global variables configuration - avoid duplicate content
// These variables will be referenced throughout the website configuration

export const SITE_VARIABLES = {{
  // Basic information
  SITE_NAME: '{self.site_name}',
  SITE_DOMAIN: '{self.domain}',
  SITE_URL: 'https://{self.domain}',
  API_DOMAIN: '{api_domain}',

  // Contact information (optional - can be commented out if not needed)
  CONTACT_EMAIL: '{self.site_info.get("contact_email", f"support@{self.domain}")}',

  // Social media (optional - can be commented out if not needed)
  // TWITTER_HANDLE: '@gocoupons',
  // TWITTER_URL: 'https://twitter.com/gocoupons',
  // FACEBOOK_URL: 'https://facebook.com/gocoupons',
  // INSTAGRAM_URL: 'https://instagram.com/gocoupons',
  // FACEBOOK_APP_ID: '123456789',

  // Technical information
  APP_VERSION: '1.0.0',
  CURRENT_YEAR: new Date().getFullYear().toString(),
  CURRENT_DATE: new Date().toLocaleDateString('en-US', {{
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }}),

  // Brand tagline
  TAGLINE: '{tagline_desc.get("tagline", "Discover, save, and enjoy more")}',
  SHORT_DESCRIPTION: '{tagline_desc.get("description", "Discover amazing discounts and deals from thousands of retailers.")}',

  // Featured functions
  FEATURES: {{
    VERIFIED_COUPONS: '{features.get("VERIFIED_COUPONS", "Verified Coupons")}',
    EXCLUSIVE_DEALS: '{features.get("EXCLUSIVE_DEALS", "Exclusive Deals")}',
    PRICE_ALERTS: '{features.get("PRICE_ALERTS", "Price Alerts")}',
    CASHBACK: '{features.get("CASHBACK", "Cashback Offers")}'
  }},

  // Environment configuration
  ENVIRONMENT: {{
    API_BASE_URL_PRODUCTION: '{self.api_url}',
    API_BASE_URL_DEVELOPMENT: 'http://localhost:8080',
    IS_PRODUCTION: true
  }}
}} as const;

// Helper function: replace variables in templates
export const replaceVariables = (template: string): string => {{
  let result = template;

  // Replace all {{{{VARIABLE_NAME}}}} format variables
  Object.entries(SITE_VARIABLES).forEach(([key, value]) => {{
    if (typeof value === 'string') {{
      const regex = new RegExp(`{{{{${{key}}}}}}`, 'g');
      result = result.replace(regex, value);
    }}
  }});

  // Handle nested objects
  Object.entries(SITE_VARIABLES.FEATURES).forEach(([key, value]) => {{
    const regex = new RegExp(`{{{{FEATURES.${{key}}}}}}`, 'g');
    result = result.replace(regex, value);
  }});

  return result;
}};

// Common combined variables
export const COMBINED_VARIABLES = {{
  FULL_SITE_TITLE: `${{SITE_VARIABLES.SITE_NAME}} - ${{SITE_VARIABLES.TAGLINE}}`,
  CONTACT_INFO: SITE_VARIABLES.CONTACT_EMAIL, // Simplified to show email only
  COPYRIGHT: `© ${{SITE_VARIABLES.CURRENT_YEAR}} ${{SITE_VARIABLES.SITE_NAME}}. All rights reserved.`,
}} as const;
"""

    def generate_branding_ts(self) -> str:
        """Generate branding.ts file content"""
        print("⏳ Generating branding.ts...")

        try:
            branding_result = self.config_generator.generate_branding_content(
                self.domain, self.site_name, self.api_url
            )

            # Set summary based on actual results
            branding_success = (branding_result and
                                branding_result.get("icon") and
                                branding_result.get("iconBg") and
                                branding_result.get("colors"))
            self.summary["branding.ts"] = {"branding": "AI" if branding_success else "Fallback"}

            colors_data = branding_result.get("colors", {})
            if not isinstance(colors_data, dict):
                colors_data = {}

            content = f"""import {{ BrandingConfig }} from '../types';
import {{ SITE_VARIABLES }} from './variables';

export const branding: BrandingConfig = {{
  logo: {{
    text: SITE_VARIABLES.SITE_NAME,
    icon: '{branding_result.get("icon", "🎫")}',
    iconBg: '{branding_result.get("iconBg", "bg-gradient-to-br from-green-400 to-blue-500")}'
  }},
  colors: {{
    primary: '{colors_data.get("primary", "#10b981")}',
    secondary: '{colors_data.get("secondary", "#3b82f6")}',
    accent: '{colors_data.get("accent", "#8b5cf6")}'
  }},
  fonts: {{
    primary: 'Inter',
    secondary: 'Inter'
  }}
}};
"""
            print("👍 branding.ts content generated successfully.")
            return content

        except Exception as e:
            print(f"❌ Error generating branding.ts: {e}")
            self.summary["branding.ts"] = {"error": str(e)}
            raise

    def generate_content_ts(self) -> str:
        """Generate content.ts file content"""
        print("⏳ Generating content.ts...")

        try:
            content_result = self.config_generator.generate_content_config(
                self.domain, self.site_name, self.api_url
            )

            # Set summary based on actual results
            content_success = (content_result and
                               content_result.get("hero") and
                               content_result.get("hero").get("searchPlaceholder") and
                               content_result.get("sections") and
                               content_result.get("buttons") and
                               content_result.get("messages") and
                               content_result.get("newsletter"))
            self.summary["content.ts"] = {"content": "AI" if content_success else "Fallback"}

            content = self._build_content_ts(content_result)
            print("👍 content.ts content generated successfully.")
            return content

        except Exception as e:
            print(f"❌ Error generating content.ts: {e}")
            self.summary["content.ts"] = {"error": str(e)}
            raise

    def _build_content_ts(self, content_data: Dict) -> str:
        """Build content.ts file content"""
        import json
        import re

        # Create final content object with placeholders for ungenerated parts
        final_content_object = {
            "hero": content_data.get("hero", {}),
            "sections": content_data.get("sections", {}),
            "buttons": content_data.get("buttons", {}),
            "messages": content_data.get("messages", {}),
            "pageContent": content_data.get("pageContent", {}),
            "newsletter": content_data.get("newsletter", {}),
            "footer": "footerConfig",  # Placeholder
            "staticPages": {
                "about": "aboutPageContent",
                "contact": "contactPageContent",
                "privacy": "privacyPageContent",
                "terms": "termsPageContent"
            }
        }

        # Generate JS object string using json.dumps, but keep double quotes
        content_json = json.dumps(final_content_object, indent=2, ensure_ascii=False)

        # Remove quotes from keys
        content_json = re.sub(r'"(\w+)":', r'\1:', content_json)

        # Replace placeholder strings with actual variable names
        content_json = content_json.replace('"footerConfig"', 'footerConfig')
        content_json = content_json.replace('"aboutPageContent"', 'aboutPageContent')
        content_json = content_json.replace('"contactPageContent"', 'contactPageContent')
        content_json = content_json.replace('"privacyPageContent"', 'privacyPageContent')
        content_json = content_json.replace('"termsPageContent"', 'termsPageContent')

        # Convert remaining double quotes to single quotes and handle escaping
        content_json = content_json.replace("'", "\\'")  # Escape existing single quotes
        content_json = content_json.replace('"', "'")  # Convert double quotes to single quotes

        return f"""// Content Configuration
// Auto-generated by main.py
import {{ ContentConfig }} from '../types';
import {{ aboutPageContent, contactPageContent, privacyPageContent, termsPageContent }} from './pages';
import {{ footerConfig }} from './footer';

export const content: ContentConfig = {content_json};
"""

    def generate_theme_ts(self) -> str:
        """Generate theme configuration file content - using advanced color generation system"""
        print("⏳ Generating advanced theme color file...")

        try:
            # Import advanced theme generator
            from ..generators.theme_generator import ThemeGenerator

            print("🎨 Using advanced color generation system...")

            # Use advanced theme generator
            theme_generator = ThemeGenerator()
            theme_config = theme_generator.generate_complete_theme(
                self.site_name,
                self.domain,
                self.api_url
            )

            if not theme_config:
                print("❌ Advanced color generation failed")
                self.summary["theme.ts"] = {"theme": "Generation failed"}
                raise Exception("Color generation failed, unable to generate theme file")

            content = self._build_theme_content(theme_config)
            print("✅ Advanced theme color file generated successfully!")
            print(f"🎯 Contains {len(theme_config.get('components', {}))} component color schemes")
            self.summary["theme.ts"] = {"theme": "AI Advanced Generation (Complete Structure)"}

            return content

        except Exception as e:
            print(f"❌ Advanced color generation failed: {e}")
            print("🔄 Falling back to original color generation system...")

            # Fallback to original system
            try:
                colors = self.config_generator.generate_theme_colors(
                    self.domain,
                    self.site_name,
                    "professional"
                )

                if not colors:
                    self.summary["theme.ts"] = {"theme": "Complete failure"}
                    raise Exception("All color generation methods failed")

                # Use original simplified structure as backup
                specific = self.config_generator.generate_theme_specific_colors(
                    colors,
                    self.domain,
                    self.site_name
                )

                gradients = self.config_generator.generate_theme_gradients(colors, specific or {})

                content = self._build_fallback_theme_content(colors, specific, gradients)
                print("⚠️ Using backup color scheme")
                self.summary["theme.ts"] = {"theme": "Backup scheme (Simplified structure)"}
                return content

            except Exception as fallback_error:
                print(f"❌ Fallback generation also failed: {fallback_error}")
                self.summary["theme.ts"] = {"theme": "Complete failure"}
                raise

    def _build_theme_content(self, theme_config: Dict) -> str:
        """Build theme content with complete structure"""
        import json

        # Generate color configuration
        colors_json = json.dumps(theme_config['colors'], indent=4)
        components_json = json.dumps(theme_config['components'], indent=4)
        gradients_json = json.dumps(theme_config['gradients'], indent=4)

        return f'''import {{ ThemeConfig }} from './types';

/**
 * {theme_config['name']}
 * {theme_config['description']}
 */
export const defaultTheme: ThemeConfig = {{
  name: '{theme_config['name']}',
  description: '{theme_config['description']}',
  version: '{theme_config['version']}',

  colors: {colors_json},

  components: {components_json},

  gradients: {gradients_json},

  borderRadius: {{
    sm: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    full: '9999px'
  }},

  spacing: {{
    xs: '0.5rem',
    sm: '0.75rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem'
  }},

  fontSize: {{
    xs: '0.75rem',
    sm: '0.875rem',
    base: '1rem',
    lg: '1.125rem',
    xl: '1.25rem',
    '2xl': '1.5rem',
    '3xl': '1.875rem',
    '4xl': '2.25rem'
  }}
}};
'''

    def _build_fallback_theme_content(self, colors: Dict, specific: Dict, gradients: Dict) -> str:
        """Build fallback theme content with simplified structure"""
        import json

        return f'''import {{ ThemeConfig }} from './types';

/**
 * {self.site_name} AI Theme (Fallback)
 * Simplified color structure
 */
export const defaultTheme: ThemeConfig = {{
  name: '{self.site_name} AI Theme',
  description: 'AI hybrid-generated theme for {self.site_name}',
  version: '2.0.0',

  colors: {json.dumps(colors, indent=4)},

  components: {json.dumps(specific or {}, indent=4)},

  gradients: {json.dumps(gradients or {}, indent=4)},

  borderRadius: {{
    sm: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    full: '9999px'
  }},

  spacing: {{
    xs: '0.5rem',
    sm: '0.75rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem'
  }},

  fontSize: {{
    xs: '0.75rem',
    sm: '0.875rem',
    base: '1rem',
    lg: '1.125rem',
    xl: '1.25rem',
    '2xl': '1.5rem',
    '3xl': '1.875rem',
    '4xl': '2.25rem'
  }}
}};
'''

    def _print_summary(self):
        """Print generation summary"""
        print("\n--- Generation Summary ---")
        for filename, sources in self.summary.items():
            print(f"\n📄 File: {filename}")
            for key, source in sources.items():
                if source == "AI" or "AI" in source:
                    print(f"  ✅ {key}: Generated by AI")
                elif "failed" in source.lower() or "error" in source.lower():
                    print(f"  ❌ {key}: Generation failed - {source}")
                else:
                    print(f"  ⚠️ {key}: Using fallback scheme")
        print("\n-----------------")

    def run(self):
        """Execute generation and saving of all configuration files"""
        print("🚀 Starting frontend configuration file generation...")

        files_to_generate = {
            "variables.ts": self.generate_variables_ts,
            "branding.ts": self.generate_branding_ts,
            "content.ts": self.generate_content_ts,
            "theme.ts": self.generate_theme_ts,
        }

        generated_files_config = self.config_loader.get_generated_files()

        for filename, generator_func in files_to_generate.items():
            if filename in generated_files_config:
                try:
                    file_content = generator_func()
                    self.file_utils.write_file(filename, file_content)
                except Exception as e:
                    print(f"❌ Failed to generate {filename}: {e}")
                    continue
            else:
                print(f"⏭️ Skipping generation of {filename} (not configured in config.yaml).")

        self._print_summary()
        print(f"\n🎉 All configuration files have been successfully generated in '{self.output_dir}' directory!")
