// Global variables configuration - avoid duplicate content
// These variables will be referenced throughout the website configuration

export const SITE_VARIABLES = {
  // Basic information
  SITE_NAME: 'CouponsGo',
  SITE_DOMAIN: 'coupons-go.org',
  SITE_URL: 'https://coupons-go.org',
  API_DOMAIN: 'api.coupons-go.org',

  // Contact information (optional - can be commented out if not needed)
  CONTACT_EMAIL: '<EMAIL>',

  // Social media (optional - can be commented out if not needed)
  // TWITTER_HANDLE: '@gocoupons',
  // TWITTER_URL: 'https://twitter.com/gocoupons',
  // FACEBOOK_URL: 'https://facebook.com/gocoupons',
  // INSTAGRAM_URL: 'https://instagram.com/gocoupons',
  // FACEBOOK_APP_ID: '123456789',

  // Technical information
  APP_VERSION: '1.0.0',
  CURRENT_YEAR: new Date().getFullYear().toString(),
  CURRENT_DATE: new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }),

  // Brand tagline
  TAGLINE: 'Discover Amazing Deals',
  SHORT_DESCRIPTION: 'Find the best coupons and deals from top brands',

  // Featured functions
  FEATURES: {
    VERIFIED_COUPONS: 'Verified Coupons',
    EXCLUSIVE_DEALS: 'Exclusive Deals',
    PRICE_ALERTS: 'Price Alerts',
    CASHBACK: 'Cashback Offers'
  },

  // Environment configuration
  ENVIRONMENT: {
    API_BASE_URL_PRODUCTION: 'http://api.coupons-go.org',
    API_BASE_URL_DEVELOPMENT: 'http://localhost:8080',
    IS_PRODUCTION: true
  }
} as const;

// Helper function: replace variables in templates
export const replaceVariables = (template: string): string => {
  let result = template;

  // Replace all {{VARIABLE_NAME}} format variables
  Object.entries(SITE_VARIABLES).forEach(([key, value]) => {
    if (typeof value === 'string') {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, value);
    }
  });

  // Handle nested objects
  Object.entries(SITE_VARIABLES.FEATURES).forEach(([key, value]) => {
    const regex = new RegExp(`{{FEATURES.${key}}}`, 'g');
    result = result.replace(regex, value);
  });

  return result;
};

// Common combined variables
export const COMBINED_VARIABLES = {
  FULL_SITE_TITLE: `${SITE_VARIABLES.SITE_NAME} - ${SITE_VARIABLES.TAGLINE}`,
  CONTACT_INFO: SITE_VARIABLES.CONTACT_EMAIL, // Simplified to show email only
  COPYRIGHT: `© ${SITE_VARIABLES.CURRENT_YEAR} ${SITE_VARIABLES.SITE_NAME}. All rights reserved.`,
} as const;
