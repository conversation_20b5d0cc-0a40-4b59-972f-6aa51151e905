#!/usr/bin/env python3
"""
New main entry point for Frontend Config Generator
Uses the refactored code structure with fallback to old structure
"""

import os
import sys
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def main():
    """Main entry point with fallback support"""
    print("🚀 Frontend Config Generator v2.0.0")
    print("📁 Using refactored code structure...")
    
    try:
        # Try to use new refactored structure
        from src.frontend_config_generator.core.main_generator import MainGenerator
        
        print("✅ Using new refactored modules")
        generator = MainGenerator()
        generator.run()
        
    except ImportError as e:
        print(f"⚠️ New structure not available: {e}")
        print("🔄 Falling back to original structure...")
        
        try:
            # Fallback to original structure
            from main import MainGenerator as OriginalMainGenerator
            
            print("✅ Using original modules")
            generator = OriginalMainGenerator()
            generator.run()
            
        except ImportError as fallback_error:
            print(f"❌ Both new and original structures failed:")
            print(f"   New: {e}")
            print(f"   Original: {fallback_error}")
            print()
            print("🔧 Please run the dependency installer first:")
            print("   python3 install_deps.py")
            sys.exit(1)
    
    except Exception as e:
        print(f"❌ Error during generation: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
