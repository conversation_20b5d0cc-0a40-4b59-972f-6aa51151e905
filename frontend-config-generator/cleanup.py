#!/usr/bin/env python3
"""
Cleanup script to remove problematic files and optimize the project
"""

import os
import shutil
import glob
from pathlib import Path


def cleanup_project():
    """Clean up the project by removing problematic and unnecessary files"""
    print("🧹 Starting project cleanup...")
    
    # Get project root
    project_root = Path(__file__).parent
    
    # Files and patterns to remove
    cleanup_patterns = [
        "**/__pycache__",
        "**/*.pyc",
        "**/*.pyo", 
        "**/*.so",  # This includes the problematic mask.cpython-311-darwin.so
        "**/.DS_Store",
        "**/Thumbs.db",
        "**/*.swp",
        "**/*.swo"
    ]
    
    # Directories to clean
    cleanup_dirs = [
        "__pycache__",
        ".pytest_cache",
        ".mypy_cache",
        "build",
        "dist",
        "*.egg-info"
    ]
    
    removed_count = 0
    
    # Remove files matching patterns
    for pattern in cleanup_patterns:
        for file_path in project_root.glob(pattern):
            try:
                if file_path.is_file():
                    file_path.unlink()
                    print(f"🗑️ Removed file: {file_path.relative_to(project_root)}")
                    removed_count += 1
                elif file_path.is_dir():
                    shutil.rmtree(file_path)
                    print(f"🗑️ Removed directory: {file_path.relative_to(project_root)}")
                    removed_count += 1
            except Exception as e:
                print(f"⚠️ Could not remove {file_path}: {e}")
    
    # Clean virtual environment if it exists and recreate it
    venv_path = project_root / "venv"
    if venv_path.exists():
        print("🔄 Cleaning virtual environment...")
        try:
            shutil.rmtree(venv_path)
            print("✅ Virtual environment removed")
            
            # Recreate virtual environment
            import subprocess
            import sys
            
            print("🔧 Creating new virtual environment...")
            subprocess.check_call([sys.executable, "-m", "venv", str(venv_path)])
            print("✅ New virtual environment created")
            
        except Exception as e:
            print(f"⚠️ Could not clean virtual environment: {e}")
    
    print(f"\n✅ Cleanup completed! Removed {removed_count} items.")
    print("\n📋 Next steps:")
    print("1. Activate virtual environment: source venv/bin/activate")
    print("2. Install dependencies: python3 install_deps.py")
    print("3. Run the generator: python3 main_new.py")


def fix_permissions():
    """Fix file permissions for security"""
    print("🔒 Fixing file permissions...")
    
    project_root = Path(__file__).parent
    
    # Make Python files executable
    for py_file in project_root.glob("**/*.py"):
        try:
            py_file.chmod(0o755)
        except Exception as e:
            print(f"⚠️ Could not fix permissions for {py_file}: {e}")
    
    print("✅ File permissions fixed")


if __name__ == "__main__":
    cleanup_project()
    fix_permissions()
    print("\n🎉 Project cleanup and security fixes completed!")
