#!/usr/bin/env python3
"""
依赖安装脚本
"""

import subprocess
import sys


def install_dependencies():
    """安装必要的Python依赖"""
    dependencies = [
        'pyyaml>=6.0',
        'requests>=2.31.0',
        'charset-normalizer>=3.0.0',  # Fix for requests warning
        'chardet>=5.0.0',  # Alternative character detection
        'g4f>=0.2.0',
        'timeout-decorator>=0.5.0',
        'colorama>=0.4.6',  # For colored terminal output
    ]

    print("🚀 Installing dependencies for Frontend Config Generator...")
    print("🔧 Fixing requests dependency warnings...")
    print()

    for dep in dependencies:
        try:
            print(f"📦 Installing {dep}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', dep])
            print(f"✅ {dep} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ {dep} installation failed: {e}")
            return False

    print()
    print("🎉 All dependencies installed successfully!")
    print("✅ Requests dependency warnings should now be resolved!")
    print()
    print("You can now run:")
    print("  python3 main.py              # Main generator")
    print("  python3 -m src.frontend_config_generator.cli  # CLI version")
    print()
    return True


if __name__ == "__main__":
    install_dependencies()
