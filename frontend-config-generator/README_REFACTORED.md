# Frontend Config Generator - Refactored Version 2.0.0

## 🎯 Overview

This is a completely refactored version of the Frontend Config Generator with improved code structure, better error handling, and enhanced maintainability.

## 🏗️ Project Structure

```
frontend-config-generator/
├── src/                                    # New refactored source code
│   └── frontend_config_generator/
│       ├── core/                          # Core modules
│       │   ├── config_loader.py          # Configuration loading
│       │   └── main_generator.py         # Main generation logic
│       ├── generators/                    # Generator modules
│       │   ├── config_generator.py       # AI-powered config generation
│       │   ├── theme_generator.py        # Advanced theme generation
│       │   └── content_generator.py      # Content generation
│       ├── utils/                         # Utility modules
│       │   ├── file_utils.py             # File operations
│       │   ├── color_utils.py            # Color manipulation
│       │   └── validation_utils.py       # Validation utilities
│       └── cli.py                         # Command line interface
├── config.yaml                           # Configuration file
├── requirements.txt                       # Python dependencies
├── setup.py                             # Package setup
├── main_new.py                           # New main entry point
├── test_refactored.py                    # Test suite
├── deploy_configs.py                     # Deployment script
├── cleanup.py                            # Cleanup script
└── install_deps.py                       # Dependency installer
```

## 🚀 Quick Start

### 1. Clean Installation

```bash
# Clean up old files and dependencies
python3 cleanup.py

# Install dependencies
python3 install_deps.py
```

### 2. Run the Generator

```bash
# Using the new main entry point
python3 main_new.py

# Or using the CLI
python3 -m src.frontend_config_generator.cli
```

### 3. Test the Refactored Code

```bash
# Run comprehensive tests
python3 test_refactored.py
```

### 4. Deploy Generated Files

```bash
# Deploy to frontend directory
python3 deploy_configs.py
```

## 🔧 Configuration

Edit `config.yaml` to customize your site settings:

```yaml
site:
  name: "YourSiteName"
  domain: "your-domain.com"
  api_url: "http://api.your-domain.com"
  contact_email: "<EMAIL>"

ai:
  model: "gemini_2_5_pro"
  proxy: ""  # Optional proxy settings

output:
  directory: "./output"

generated_files:
  - "variables.ts"
  - "branding.ts"
  - "content.ts"
  - "theme.ts"
```

## 🎨 Generated Files

The generator creates four main configuration files:

1. **variables.ts** - Global site variables and constants
2. **branding.ts** - Brand colors, logos, and visual identity
3. **content.ts** - Website content and copy
4. **theme.ts** - Complete theme configuration with colors and components

## 🛠️ Key Improvements

### Code Structure
- ✅ Standard Python package structure
- ✅ Proper module separation and imports
- ✅ Clear separation of concerns
- ✅ Comprehensive error handling

### Dependency Management
- ✅ Fixed requests dependency warnings
- ✅ Proper requirements.txt with version pinning
- ✅ Removed problematic binary files (*.so)
- ✅ Clean virtual environment setup

### Error Handling
- ✅ Graceful fallbacks when AI services fail
- ✅ Comprehensive error logging
- ✅ Validation at multiple levels
- ✅ Safe file operations

### Theme Generation
- ✅ Fixed JSON generation issues
- ✅ Robust fallback color schemes
- ✅ Complete component color mapping
- ✅ Gradient generation with error handling

## 🧪 Testing

The project includes comprehensive tests:

```bash
# Run all tests
python3 test_refactored.py

# Test specific components
python3 -c "from test_refactored import test_theme_generator; test_theme_generator()"
```

## 🚨 Troubleshooting

### Common Issues

1. **Import Errors**
   ```bash
   # Ensure you're in the correct directory
   cd frontend-config-generator
   
   # Check Python path
   python3 -c "import sys; print(sys.path)"
   ```

2. **AI Service Unavailable**
   - The generator will automatically use fallback data
   - Check your proxy settings in config.yaml
   - Verify internet connectivity

3. **File Permission Issues**
   ```bash
   # Fix permissions
   python3 cleanup.py
   ```

4. **Dependency Issues**
   ```bash
   # Reinstall dependencies
   python3 install_deps.py
   ```

## 📁 Output Files

Generated files are saved to the `output/` directory and can be deployed using:

```bash
python3 deploy_configs.py
```

This will copy files to the appropriate locations in your frontend project:
- `variables.ts` → `coupon-frontend/src/config/site/variables.ts`
- `branding.ts` → `coupon-frontend/src/config/site/branding.ts`
- `content.ts` → `coupon-frontend/src/config/site/content.ts`
- `theme.ts` → `coupon-frontend/src/config/theme/default.ts`

## 🔄 Migration from Old Version

The refactored version maintains backward compatibility:

1. Old configuration files continue to work
2. Fallback to original code if new structure fails
3. Same output format and file structure
4. Enhanced error handling and reliability

## 📝 Development

To extend the generator:

1. Add new generators in `src/frontend_config_generator/generators/`
2. Add utilities in `src/frontend_config_generator/utils/`
3. Update the main generator in `src/frontend_config_generator/core/main_generator.py`
4. Add tests in `test_refactored.py`

## 🎉 Success Indicators

When everything is working correctly, you should see:

- ✅ All imports successful
- ✅ Configuration loaded
- ✅ Files generated without errors
- ✅ Deployment completed
- ✅ Files verified in target locations

The refactored version provides a much more robust and maintainable codebase while preserving all the original functionality.
