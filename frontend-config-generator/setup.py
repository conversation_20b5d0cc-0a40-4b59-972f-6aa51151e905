#!/usr/bin/env python3
"""
Setup script for Frontend Config Generator
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="frontend-config-generator",
    version="2.0.0",
    author="CouponDogg Team",
    author_email="<EMAIL>",
    description="AI-powered frontend configuration generator for coupon websites",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/coupondogg/frontend-config-generator",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "frontend-config-gen=frontend_config_generator.cli:main",
        ],
    },
    include_package_data=True,
    package_data={
        "frontend_config_generator": ["templates/*.yaml", "config/*.yaml"],
    },
)
