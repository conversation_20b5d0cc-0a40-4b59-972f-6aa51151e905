#!/usr/bin/env node

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 从 variables.ts 读取配置
async function loadConfig() {
  try {
    const tempDir = path.join(__dirname, '../dist-temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    console.log('📖 Loading configuration from variables.ts...');
    execSync(`npx tsc --outDir ${tempDir} --target es2020 --module es2020 --moduleResolution node --skipLibCheck true src/config/site/variables.ts`, { 
      cwd: path.join(__dirname, '..'),
      stdio: 'pipe'
    });
    
    const configModulePath = path.join(tempDir, 'variables.js');
    const { SITE_VARIABLES } = await import(configModulePath);
    
    return {
      api_url: SITE_VARIABLES.ENVIRONMENT.IS_PRODUCTION ? 
        SITE_VARIABLES.ENVIRONMENT.API_BASE_URL_PRODUCTION : 
        SITE_VARIABLES.ENVIRONMENT.API_BASE_URL_DEVELOPMENT,
      site_url: SITE_VARIABLES.SITE_URL,
      site_domain: SITE_VARIABLES.SITE_DOMAIN,
      api_domain: SITE_VARIABLES.API_DOMAIN
    };
  } catch (error) {
    console.error('❌ Failed to load variables.ts:', error.message);
    process.exit(1);
  }
}

// 执行命令
function runCommand(command, description) {
  console.log(`\n📋 ${description}...`);
  console.log(`💻 Running: ${command}`);
  
  try {
    execSync(command, { 
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    });
    console.log(`✅ ${description} completed`);
    return true;
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    return false;
  }
}

// 主函数
async function main() {
  console.log('🚀 Starting CouponsGo Live Build Process...\n');
  console.log('📦 This process will:');
  console.log('   ✨ Load configuration from variables.ts');
  console.log('   📊 Prebuild data from backend API');
  console.log('   🎨 Generate theme files');
  console.log('   🔨 Build the application');
  console.log('   📝 Process HTML templates');
  console.log('   🗂️  Prepare static assets\n');

  const startTime = Date.now();

  // 1. Load configuration from variables.ts
  console.log('🔧 Loading configuration...');
  const config = await loadConfig();
  
  console.log(`📡 API URL: ${config.api_url}`);
  console.log(`🌐 Site URL: ${config.site_url}`);
  console.log(`🏷️  Site Domain: ${config.site_domain}`);
  console.log(`🔗 API Domain: ${config.api_domain}`);

  let success = true;

  // 2. Prebuild data from backend API
  console.log('\n🎯 Starting prebuild process...');
  const dataCommand = `node scripts/prebuild-data.mjs --api-url=${config.api_url}`;
  const dataSuccess = runCommand(dataCommand, 'Data Prebuild');

  if (!dataSuccess) {
    console.log('⚠️  Data prebuild failed, but continuing with build...');
    // Allow build to continue even if data prebuild fails
  }

  // 3. Generate theme
  if (success) {
    success = runCommand('node scripts/generate-theme.mjs', 'Theme Generation') && success;
  }

  // 4. Build application
  if (success) {
    success = runCommand('tsc && vite build', 'Application Build') && success;
  }

  // 5. Process HTML templates
  if (success) {
    success = runCommand('node scripts/process-html.mjs', 'HTML Template Processing') && success;
  }

  // 6. Copy static data to dist
  if (success) {
    const staticDataSrc = path.join(__dirname, '../public/static-data');
    const staticDataDest = path.join(__dirname, '../dist/static-data');
    
    if (fs.existsSync(staticDataSrc)) {
      if (!fs.existsSync(staticDataDest)) {
        fs.mkdirSync(staticDataDest, { recursive: true });
      }
      
      // Copy static data files
      const files = fs.readdirSync(staticDataSrc);
      files.forEach(file => {
        fs.copyFileSync(
          path.join(staticDataSrc, file),
          path.join(staticDataDest, file)
        );
      });
      console.log(`📊 Copied ${files.length} static data files to dist/`);
    }
  }

  // Build summary
  const duration = Math.round((Date.now() - startTime) / 1000);
  console.log('\n' + '='.repeat(60));
  
  if (success) {
    console.log('🎉 Live Build Completed Successfully!');
    console.log(`⏱️  Total time: ${duration}s`);
    console.log(`📁 Output directory: dist/`);
    
    // Show build artifacts info
    try {
      const distStats = fs.statSync(path.join(__dirname, '../dist'));
      console.log(`📦 Build timestamp: ${distStats.mtime.toLocaleString()}`);
      
      // Check static data
      const staticDataDir = path.join(__dirname, '../dist/static-data');
      if (fs.existsSync(staticDataDir)) {
        const files = fs.readdirSync(staticDataDir);
        console.log(`📊 Static data files: ${files.length} files`);
        console.log(`    Files: ${files.join(', ')}`);
      }
      
      // Check index.html
      const indexFile = path.join(__dirname, '../dist/index.html');
      if (fs.existsSync(indexFile)) {
        const indexStats = fs.statSync(indexFile);
        console.log(`📄 Index file: ${Math.round(indexStats.size / 1024)}KB`);
      }
      
    } catch (error) {
      // Ignore stats errors
    }
    
    console.log('\n🚀 Your site is ready for deployment!');
    console.log('📋 Configuration summary:');
    console.log(`   🌐 Site URL: ${config.site_url}`);
    console.log(`   📡 API URL: ${config.api_url}`);
    console.log('');
    console.log('📊 Prebuilt data includes:');
    console.log('   🏠 Home page data (categories, featured items)');
    console.log('   📂 All categories data');
    console.log('   🏢 Brands (first 5 pages)');
    console.log('   🎫 Coupons (first 5 pages)');
    console.log('   🎯 Deals (first 5 pages)');
    console.log('');
    console.log('⚡ Users will see instant loading for:');
    console.log('   - Home page');
    console.log('   - Category pages');
    console.log('   - First 5 pages of brands, coupons, deals');
  } else {
    console.log('❌ Live Build Failed');
    console.log('Please check the error messages above');
    process.exit(1);
  }
}

// Run main function
main().catch(error => {
  console.error('💥 Build process error:', error);
  process.exit(1);
});