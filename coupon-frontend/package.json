{"name": "coupon-frontend-react", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "npm run build:theme && tsc && vite build", "build:complete": "npm run build:theme && tsc && vite build && npm run process-html", "build:live": "node scripts/build-live.mjs", "build:theme": "node scripts/generate-theme.mjs", "build:theme:env": "node scripts/generate-theme.mjs", "build:sitemap": "node scripts/generate-sitemap.mjs", "prebuild:data": "node scripts/prebuild-data.mjs", "build:env": "node scripts/build-with-env.mjs", "build:dev": "npm run build:env -- --env development", "build:staging": "npm run build:env -- --env staging", "build:prod": "npm run build:env -- --env production", "verify": "node scripts/verify-build.mjs", "process-html": "node scripts/process-html.mjs", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"axios": "^1.11.0", "clsx": "^2.1.1", "js-yaml": "^4.1.0", "lucide-react": "^0.526.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "react-router-dom": "^6.26.1", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@types/js-yaml": "^4.0.9", "@types/node": "^20.14.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.13.1", "@typescript-eslint/parser": "^7.13.1", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.21", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "typescript": "^5.2.2", "vite": "^5.3.1"}}