// 配置文件类型定义

import { ThemeConfig } from './theme/types';

export interface AdUnit {
  id: number;
  name: string;
  position: 'modal' | 'sidebar' | 'in-article' | 'direct-link';
  is_active: boolean;
  priority: number;
  html: string;
  adType: 'adsense' | 'adsterra' | 'monetag' | 'other';
  maxClickRate: number;
  maxShowRate: number;
  showCount?: number;
  clickCount?: number;
  clickRate?: number;
  showRate?: number;
}

export interface SeoConfig {
  title: string;
  description: string;
  keywords: string[];
  ogImage: string;
  siteName: string;
  siteUrl: string;
  twitterHandle?: string; // 可选
  facebookAppId?: string; // 可选
}

export interface SiteConfig {
  name: string;
  description: string;
  url: string;
  defaultLanguage: string;
  supportedLanguages: string[];
  apiBaseUrl: string;
  ogImage: string;
  links?: { // 可选
    twitter?: string;
    facebook?: string;
    instagram?: string;
  };
  contact: {
    email: string;
    phone?: string; // 可选
    address?: string; // 可选
  };
}

export interface NavigationItem {
  name: string;
  href: string;
  icon?: string;
}

export interface LanguageOption {
  code: string;
  name: string;
  flag?: string;
}

export interface BrandingConfig {
  logo: {
    text: string;
    icon: string;
    iconBg: string;
  };
  colors: {
    primary: string;
    secondary: string;
    accent: string;
  };
  fonts: {
    primary: string;
    secondary: string;
  };
}

export interface PageContent {
  title: string;
  subtitle?: string;
  content: string;
  sections?: {
    [key: string]: {
      title: string;
      content: string;
    };
  };
}

export interface FooterConfig {
  description: string;
  copyright: string;
  showCategories: boolean;
  showBrands: boolean;
  statistics: {
    brands: number;
    coupons: number;
    deals: number;
  };
  newsletter: {
    title: string;
    description: string;
    placeholder: string;
    buttonText: string;
  };
  trustBadges: {
    worldwide: string;
    secure: string;
    updated: string;
  };
  sections: {
    categories: string;
    brands: string;
  };
  links: {
    company: {
      title: string;
      items: Array<{
        name: string;
        href: string;
      }>;
    };
    support?: {
      title: string;
      items: Array<{
        name: string;
        href: string;
      }>;
    };
    legal: {
      title: string;
      items: Array<{
        name: string;
        href: string;
      }>;
    };
  };
  social?: {
    title: string;
    items: Array<{
      name: string;
      href: string;
      icon: string;
    }>;
  };
}

export interface ContentConfig {
  hero: {
    searchPlaceholder: string;
  };
  sections: {
    categories: string;
    brands: string;
    featuredCoupons: string;
    featuredDeals: string;
  };
  buttons: {
    search: string;
    viewAll: string;
    getCoupon: string;
    getDeal: string;
    subscribe: string;
    unsubscribe: string;
  };
  messages: {
    loading: string;
    noResults: string;
    error: string;
    success: string;
  };
  pageContent: {
    brands: {
      title: string;
      subtitle: string;
      filterTitle: string;
      noResultsTitle: string;
      noResultsMessage: string;
    };
    coupons: {
      title: string;
      subtitle: string;
      filterTitle: string;
      noResultsTitle: string;
      noResultsMessage: string;
    };
    deals: {
      title: string;
      subtitle: string;
      filterTitle: string;
      noResultsTitle: string;
      noResultsMessage: string;
    };
    categories: {
      title: string;
      subtitle: string;
      noResultsTitle: string;
      noResultsMessage: string;
    };
    blog: {
      title: string;
      subtitle: string;
      noResultsTitle: string;
      noResultsMessage: string;
      backToBlog: string;
      articleNotFound: string;
      articleNotFoundMessage: string;
      showingResults: string;
    };
    search: {
      title: string;
      subtitle: string;
      placeholder: string;
      noResultsTitle: string;
      noResultsMessage: string;
    };
  };
  newsletter: {
    title: string;
    description: string;
    placeholder: string;
    benefits: string[];
    privacy: string;
    successTitle: string;
    successMessage: string;
  };
  footer: FooterConfig;
  staticPages: {
    about: PageContent;
    contact: PageContent;
    privacy: PageContent;
    terms: PageContent;
  };
}

export interface Config {
  modalAdTopProbability: number;
  adUnits: AdUnit[];
  headList: string[];
  bodyList: string[];
  seoConfig: SeoConfig;
  siteConfig: SiteConfig;
  navigation: NavigationItem[];
  languages: LanguageOption[];
  branding: BrandingConfig;
  content: ContentConfig;
  theme: ThemeConfig;
}
