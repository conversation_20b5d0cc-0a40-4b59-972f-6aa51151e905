/* 主题CSS变量 - 自动生成 */
/* 基于 CouponsGo Theme v1.0.0 */
:root {
  --color-primary-50: #f0fdf4;
  --color-primary-100: #dcfce7;
  --color-primary-200: #bbf7d0;
  --color-primary-300: #86efac;
  --color-primary-400: #4ade80;
  --color-primary-500: #22c55e;
  --color-primary-600: #16a34a;
  --color-primary-700: #15803d;
  --color-primary-800: #166534;
  --color-primary-900: #14532d;
  --color-primary-950: #052e16;
  --color-secondary-50: #eff6ff;
  --color-secondary-100: #dbeafe;
  --color-secondary-200: #bfdbfe;
  --color-secondary-300: #93c5fd;
  --color-secondary-400: #60a5fa;
  --color-secondary-500: #3b82f6;
  --color-secondary-600: #2563eb;
  --color-secondary-700: #1d4ed8;
  --color-secondary-800: #1e40af;
  --color-secondary-900: #1e3a8a;
  --color-secondary-950: #172554;
  --color-accent-50: #faf5ff;
  --color-accent-100: #f3e8ff;
  --color-accent-200: #e9d5ff;
  --color-accent-300: #d8b4fe;
  --color-accent-400: #c084fc;
  --color-accent-500: #a855f7;
  --color-accent-600: #9333ea;
  --color-accent-700: #7c3aed;
  --color-accent-800: #6b21a8;
  --color-accent-900: #581c87;
  --color-accent-950: #3b0764;
  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-200: #bbf7d0;
  --color-success-300: #86efac;
  --color-success-400: #4ade80;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  --color-success-800: #166534;
  --color-success-900: #14532d;
  --color-success-950: #052e16;
  --color-warning-50: #fefce8;
  --color-warning-100: #fef9c3;
  --color-warning-200: #fef08a;
  --color-warning-300: #fde047;
  --color-warning-400: #facc15;
  --color-warning-500: #eab308;
  --color-warning-600: #ca8a04;
  --color-warning-700: #a16207;
  --color-warning-800: #854d0e;
  --color-warning-900: #713f12;
  --color-warning-950: #422006;
  --color-error-50: #fef2f2;
  --color-error-100: #fee2e2;
  --color-error-200: #fecaca;
  --color-error-300: #fca5a5;
  --color-error-400: #f87171;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;
  --color-error-800: #991b1b;
  --color-error-900: #7f1d1d;
  --color-error-950: #450a0a;
  --color-info-50: #eff6ff;
  --color-info-100: #dbeafe;
  --color-info-200: #bfdbfe;
  --color-info-300: #93c5fd;
  --color-info-400: #60a5fa;
  --color-info-500: #3b82f6;
  --color-info-600: #2563eb;
  --color-info-700: #1d4ed8;
  --color-info-800: #1e40af;
  --color-info-900: #1e3a8a;
  --color-info-950: #172554;
  --color-neutral-50: #fafafa;
  --color-neutral-100: #f5f5f5;
  --color-neutral-200: #e5e5e5;
  --color-neutral-300: #d4d4d4;
  --color-neutral-400: #a3a3a3;
  --color-neutral-500: #737373;
  --color-neutral-600: #525252;
  --color-neutral-700: #404040;
  --color-neutral-800: #262626;
  --color-neutral-900: #171717;
  --color-neutral-950: #0a0a0a;
  --component-brandCard-container-background: #ffffff;
  --component-brandCard-container-border: #e5e7eb;
  --component-brandCard-container-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --component-brandCard-container-hover-background: #fafafa;
  --component-brandCard-container-hover-border: #16a34a;
  --component-brandCard-container-hover-shadow: 0 4px 16px rgba(34, 197, 94, 0.2);
  --component-brandCard-brandName-text: #16a34a;
  --component-brandCard-brandName-border: #dcfce7;
  --component-brandCard-brandName-background: rgba(220, 252, 231, 0.5);
  --component-brandCard-discountBadge-background: linear-gradient(to right, #dc2626, #b91c1c);
  --component-brandCard-discountBadge-text: #ffffff;
  --component-brandCard-discountBadge-border: transparent;
  --component-brandCard-couponCount-text: #16a34a;
  --component-brandCard-couponCount-background: rgba(34, 197, 94, 0.1);
  --component-brandCard-description-text: #6b7280;
  --component-couponCard-container-background: #ffffff;
  --component-couponCard-container-border: #e5e7eb;
  --component-couponCard-container-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  --component-couponCard-container-hover-background: #ffffff;
  --component-couponCard-container-hover-border: #16a34a;
  --component-couponCard-container-hover-shadow: 0 8px 25px rgba(34, 197, 94, 0.2);
  --component-couponCard-discountBadge-background: linear-gradient(to right, #dc2626, #b91c1c);
  --component-couponCard-discountBadge-text: #ffffff;
  --component-couponCard-discountBadge-border: transparent;
  --component-couponCard-button-background: #16a34a;
  --component-couponCard-button-text: #ffffff;
  --component-couponCard-button-border: transparent;
  --component-couponCard-button-hover: #15803d;
  --component-couponCard-button-disabled-background: #d1d5db;
  --component-couponCard-button-disabled-text: #9ca3af;
  --component-couponCard-code-text: #15803d;
  --component-couponCard-code-background: rgba(21, 128, 61, 0.1);
  --component-couponCard-code-border: #dcfce7;
  --component-couponCard-brandName-text: #6b7280;
  --component-couponCard-brandName-hover: #16a34a;
  --component-couponCard-couponName-text: #111827;
  --component-couponCard-description-text: #9ca3af;
  --component-couponCard-expiry-text: #ca8a04;
  --component-couponCard-expiry-background: rgba(202, 138, 4, 0.1);
  --component-categoryCard-container-background: #ffffff;
  --component-categoryCard-container-border: #e5e7eb;
  --component-categoryCard-container-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  --component-categoryCard-container-hover-background: #fafafa;
  --component-categoryCard-container-hover-border: #16a34a;
  --component-categoryCard-container-hover-shadow: 0 4px 16px rgba(34, 197, 94, 0.15);
  --component-categoryCard-icon-color: #16a34a;
  --component-categoryCard-icon-background: rgba(34, 197, 94, 0.1);
  --component-categoryCard-text-color: #111827;
  --component-categoryCard-text-hover: #16a34a;
  --component-categoryCard-count-text: #6b7280;
  --component-categoryCard-count-background: rgba(107, 114, 128, 0.1);
  --component-dealCard-container-background: #ffffff;
  --component-dealCard-container-border: #e5e7eb;
  --component-dealCard-container-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  --component-dealCard-container-hover-background: #ffffff;
  --component-dealCard-container-hover-border: #2563eb;
  --component-dealCard-container-hover-shadow: 0 8px 25px rgba(37, 99, 235, 0.15);
  --component-dealCard-discountBadge-background: linear-gradient(to right, #dc2626, #b91c1c);
  --component-dealCard-discountBadge-text: #ffffff;
  --component-dealCard-discountBadge-border: transparent;
  --component-dealCard-button-background: #15803d;
  --component-dealCard-button-text: #ffffff;
  --component-dealCard-button-border: transparent;
  --component-dealCard-button-hover: #15803d;
  --component-dealCard-button-disabled-background: #d1d5db;
  --component-dealCard-button-disabled-text: #9ca3af;
  --component-dealCard-brandName-text: #6b7280;
  --component-dealCard-brandName-hover: #2563eb;
  --component-dealCard-dealName-text: #111827;
  --component-dealCard-description-text: #9ca3af;
  --component-dealCard-expiry-text: #ca8a04;
  --component-dealCard-expiry-background: rgba(202, 138, 4, 0.1);
  --component-dealCard-originalPrice-text: #9ca3af;
  --component-dealCard-salePrice-text: #16a34a;
  --component-couponModal-overlay-background: rgba(0, 0, 0, 0.5);
  --component-couponModal-card-background: #ffffff;
  --component-couponModal-card-border: #e5e7eb;
  --component-couponModal-card-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  --component-couponModal-header-background: linear-gradient(to bottom right, #dbeafe, #dcfce7, #fef9c3);
  --component-couponModal-header-border: #e5e7eb;
  --component-couponModal-closeButton-color: #6b7280;
  --component-couponModal-closeButton-hover: #111827;
  --component-couponModal-code-text: #15803d;
  --component-couponModal-code-background: rgba(21, 128, 61, 0.1);
  --component-couponModal-code-border: #dcfce7;
  --component-couponModal-copyButton-background: #16a34a;
  --component-couponModal-copyButton-text: #ffffff;
  --component-couponModal-copyButton-border: transparent;
  --component-couponModal-copyButton-hover: #15803d;
  --component-couponModal-actionButton-background: #2563eb;
  --component-couponModal-actionButton-text: #ffffff;
  --component-couponModal-actionButton-border: transparent;
  --component-couponModal-actionButton-hover: #1d4ed8;
  --component-couponModal-discountBadge-background: linear-gradient(to right, #dc2626, #b91c1c);
  --component-couponModal-discountBadge-text: #ffffff;
  --component-couponModal-discountBadge-border: transparent;
  --component-couponModal-brandName-text: #6b7280;
  --component-couponModal-couponName-text: #111827;
  --component-couponModal-description-text: #9ca3af;
  --component-couponModal-expiry-text: #ca8a04;
  --component-couponModal-expiry-background: rgba(202, 138, 4, 0.1);
  --component-couponModal-terms-text: #6b7280;
  --component-couponModal-terms-background: #fafafa;
  --component-dealModal-overlay-background: rgba(0, 0, 0, 0.5);
  --component-dealModal-card-background: #ffffff;
  --component-dealModal-card-border: #e5e7eb;
  --component-dealModal-card-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  --component-dealModal-header-background: linear-gradient(to bottom right, #dbeafe, #dcfce7, #fef9c3);
  --component-dealModal-header-border: #e5e7eb;
  --component-dealModal-closeButton-color: #6b7280;
  --component-dealModal-closeButton-hover: #111827;
  --component-dealModal-actionButton-background: #15803d;
  --component-dealModal-actionButton-text: #ffffff;
  --component-dealModal-actionButton-border: transparent;
  --component-dealModal-actionButton-hover: #15803d;
  --component-dealModal-discountBadge-background: linear-gradient(to right, #dc2626, #b91c1c);
  --component-dealModal-discountBadge-text: #ffffff;
  --component-dealModal-discountBadge-border: transparent;
  --component-dealModal-brandName-text: #6b7280;
  --component-dealModal-dealName-text: #111827;
  --component-dealModal-description-text: #9ca3af;
  --component-dealModal-expiry-text: #ca8a04;
  --component-dealModal-expiry-background: rgba(202, 138, 4, 0.1);
  --component-dealModal-originalPrice-text: #9ca3af;
  --component-dealModal-salePrice-text: #16a34a;
  --component-dealModal-terms-text: #6b7280;
  --component-dealModal-terms-background: #fafafa;
  --component-commonButton-primary-background: #16a34a;
  --component-commonButton-primary-text: #ffffff;
  --component-commonButton-primary-border: transparent;
  --component-commonButton-primary-hover: #15803d;
  --component-commonButton-primary-disabled-background: #d1d5db;
  --component-commonButton-primary-disabled-text: #9ca3af;
  --component-commonButton-secondary-background: #2563eb;
  --component-commonButton-secondary-text: #ffffff;
  --component-commonButton-secondary-border: transparent;
  --component-commonButton-secondary-hover: #1d4ed8;
  --component-commonButton-secondary-disabled-background: #d1d5db;
  --component-commonButton-secondary-disabled-text: #9ca3af;
  --component-commonButton-tertiary-background: transparent;
  --component-commonButton-tertiary-text: #16a34a;
  --component-commonButton-tertiary-border: #16a34a;
  --component-commonButton-tertiary-hover: #dcfce7;
  --component-commonButton-tertiary-disabled-background: transparent;
  --component-commonButton-tertiary-disabled-text: #9ca3af;
  --component-searchBox-container-background: #ffffff;
  --component-searchBox-container-border: #e5e7eb;
  --component-searchBox-container-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  --component-searchBox-container-focus-border: #16a34a;
  --component-searchBox-container-focus-shadow: 0 0 0 3px rgba(34, 197, 94, 0.2);
  --component-searchBox-input-text: #111827;
  --component-searchBox-input-placeholder: #9ca3af;
  --component-searchBox-input-background: transparent;
  --component-searchBox-icon-color: #16a34a;
  --component-searchBox-icon-hover: #15803d;
  --component-searchBox-button-background: #16a34a;
  --component-searchBox-button-text: #ffffff;
  --component-searchBox-button-hover: #15803d;
  --component-newsletter-container-background: linear-gradient(to bottom right, #dbeafe, #dcfce7, #fef9c3);
  --component-newsletter-container-border: transparent;
  --component-newsletter-title-text: #111827;
  --component-newsletter-description-text: #374151;
  --component-newsletter-input-background: #ffffff;
  --component-newsletter-input-text: #111827;
  --component-newsletter-input-placeholder: #9ca3af;
  --component-newsletter-input-border: #e5e7eb;
  --component-newsletter-input-focus-border: #16a34a;
  --component-newsletter-input-focus-shadow: 0 0 0 3px rgba(34, 197, 94, 0.2);
  --component-newsletter-button-background: #16a34a;
  --component-newsletter-button-text: #ffffff;
  --component-newsletter-button-border: transparent;
  --component-newsletter-button-hover: #15803d;
  --component-newsletter-button-disabled-background: #d1d5db;
  --component-newsletter-button-disabled-text: #9ca3af;
  --component-newsletter-successMessage-text: #16a34a;
  --component-newsletter-successMessage-background: rgba(34, 197, 94, 0.1);
  --component-newsletter-errorMessage-text: #dc2626;
  --component-newsletter-errorMessage-background: rgba(220, 38, 38, 0.1);
  --component-footer-container-background: #111827;
  --component-footer-container-border: #374151;
  --component-footer-section-title: #ffffff;
  --component-footer-section-text: #d1d5db;
  --component-footer-link-text: #d1d5db;
  --component-footer-link-hover: #16a34a;
  --component-footer-icon-color: #16a34a;
  --component-footer-icon-hover: #4ade80;
  --component-footer-socialIcon-color: #d1d5db;
  --component-footer-socialIcon-hover: #16a34a;
  --component-footer-socialIcon-background: rgba(34, 197, 94, 0.1);
  --component-footer-copyright-text: #9ca3af;
  --component-footer-divider-color: #374151;
  --component-header-container-background: #ffffff;
  --component-header-container-border: #e5e7eb;
  --component-header-container-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  --component-header-logo-text: #111827;
  --component-header-logo-hover: #16a34a;
  --component-header-navLink-text: #6b7280;
  --component-header-navLink-hover: #16a34a;
  --component-header-navLink-active: #16a34a;
  --component-header-mobileMenuButton-color: #6b7280;
  --component-header-mobileMenuButton-hover: #16a34a;
  --component-header-mobileMenu-background: #ffffff;
  --component-header-mobileMenu-border: #e5e7eb;
  --component-header-mobileMenu-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  --component-homepage-hero-background: linear-gradient(to bottom right, #dbeafe, #dcfce7, #fef9c3);
  --component-homepage-hero-overlay: rgba(0, 0, 0, 0.1);
  --component-homepage-hero-title: #111827;
  --component-homepage-hero-subtitle: #374151;
  --component-homepage-hero-description: #6b7280;
  --component-homepage-section-background: #ffffff;
  --component-homepage-section-title: #111827;
  --component-homepage-section-subtitle: #374151;
  --component-homepage-section-description: #6b7280;
  --component-homepage-stats-number: #16a34a;
  --component-homepage-stats-label: #6b7280;
  --component-homepage-stats-background: rgba(34, 197, 94, 0.05);
  --component-homepage-feature-icon: #16a34a;
  --component-homepage-feature-title: #111827;
  --component-homepage-feature-description: #6b7280;
  --component-homepage-feature-background: #fafafa;
  --component-pagination-container-background: #ffffff;
  --component-pagination-container-border: #e5e7eb;
  --component-pagination-button-background: #fafafa;
  --component-pagination-button-text: #6b7280;
  --component-pagination-button-border: #e5e7eb;
  --component-pagination-button-hover: #e5e7eb;
  --component-pagination-button-active: #16a34a;
  --component-pagination-button-disabled-background: #f5f5f5;
  --component-pagination-button-disabled-text: #d1d5db;
  --component-pagination-info-text: #6b7280;
  --component-loading-spinner-primary: #16a34a;
  --component-loading-spinner-secondary: #dcfce7;
  --component-loading-skeleton-background: #f5f5f5;
  --component-loading-skeleton-highlight: #e5e7eb;
  --component-loading-overlay-background: rgba(255, 255, 255, 0.8);
  --component-form-label-text: #111827;
  --component-form-label-required: #dc2626;
  --component-form-input-background: #ffffff;
  --component-form-input-text: #111827;
  --component-form-input-placeholder: #9ca3af;
  --component-form-input-border: #e5e7eb;
  --component-form-input-focus-border: #16a34a;
  --component-form-input-focus-shadow: 0 0 0 3px rgba(34, 197, 94, 0.2);
  --component-form-input-error-border: #dc2626;
  --component-form-input-error-text: #dc2626;
  --component-form-input-error-background: rgba(220, 38, 38, 0.05);
  --component-form-select-background: #ffffff;
  --component-form-select-text: #111827;
  --component-form-select-border: #e5e7eb;
  --component-form-select-arrow: #6b7280;
  --component-form-select-focus-border: #16a34a;
  --component-form-select-focus-shadow: 0 0 0 3px rgba(34, 197, 94, 0.2);
  --component-form-checkbox-background: #ffffff;
  --component-form-checkbox-border: #e5e7eb;
  --component-form-checkbox-checked-background: #16a34a;
  --component-form-checkbox-checked-border: #16a34a;
  --component-form-checkbox-checked-checkmark: #ffffff;
  --component-form-radio-background: #ffffff;
  --component-form-radio-border: #e5e7eb;
  --component-form-radio-checked-background: #ffffff;
  --component-form-radio-checked-border: #16a34a;
  --component-form-radio-checked-dot: #16a34a;
  --component-notification-success-background: rgba(34, 197, 94, 0.1);
  --component-notification-success-text: #16a34a;
  --component-notification-success-border: #86efac;
  --component-notification-success-icon: #16a34a;
  --component-notification-error-background: rgba(220, 38, 38, 0.1);
  --component-notification-error-text: #dc2626;
  --component-notification-error-border: #fca5a5;
  --component-notification-error-icon: #dc2626;
  --component-notification-warning-background: rgba(202, 138, 4, 0.1);
  --component-notification-warning-text: #ca8a04;
  --component-notification-warning-border: #fde047;
  --component-notification-warning-icon: #ca8a04;
  --component-notification-info-background: rgba(37, 99, 235, 0.1);
  --component-notification-info-text: #2563eb;
  --component-notification-info-border: #93c5fd;
  --component-notification-info-icon: #2563eb;
  --component-badge-primary-background: #16a34a;
  --component-badge-primary-text: #ffffff;
  --component-badge-primary-border: transparent;
  --component-badge-secondary-background: #2563eb;
  --component-badge-secondary-text: #ffffff;
  --component-badge-secondary-border: transparent;
  --component-badge-success-background: #16a34a;
  --component-badge-success-text: #ffffff;
  --component-badge-success-border: transparent;
  --component-badge-warning-background: #ca8a04;
  --component-badge-warning-text: #ffffff;
  --component-badge-warning-border: transparent;
  --component-badge-error-background: #dc2626;
  --component-badge-error-text: #ffffff;
  --component-badge-error-border: transparent;
  --component-badge-info-background: #2563eb;
  --component-badge-info-text: #ffffff;
  --component-badge-info-border: transparent;
  --gradient-hero: linear-gradient(to bottom right, #dbeafe, #dcfce7, #fef9c3);
  --gradient-card: linear-gradient(to bottom right, transparent, #f9fafb, #f3f4f6);
  --gradient-button: linear-gradient(to right, #16a34a, #15803d);
  --gradient-discount: linear-gradient(to right, #dc2626, #b91c1c);
  --gradient-hover-primary: linear-gradient(to bottom right, #dcfce7, #bbf7d0, #86efac);
  --gradient-hover-secondary: linear-gradient(to bottom right, #dbeafe, #bfdbfe, #93c5fd);
  --gradient-hover-accent: linear-gradient(to bottom right, #f3e8ff, #e9d5ff, #d8b4fe);
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 0.75rem;
  --border-radius-full: 9999px;
  --spacing-xs: 0.5rem;
  --spacing-sm: 0.75rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
}