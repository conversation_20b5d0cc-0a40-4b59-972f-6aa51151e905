import { AdUnit } from '@/config/types';
import { getConfig } from '@/config';

// 广告统计数据接口
interface AdStats {
  modalOpenCount: number;
  adStats: {
    [adId: number]: {
      showCount: number;
      clickCount: number;
      showRate: number;
      clickRate: number;
    };
  };
}

import { SITE_VARIABLES } from '@/config/site/variables';

// 本地存储键名 - 基于域名，确保多站点不冲突
const AD_STATS_KEY = `${SITE_VARIABLES.SITE_DOMAIN.replace(/\./g, '_')}_ad_stats`;

// 获取广告统计数据
function getAdStats(): AdStats {
  if (typeof window === 'undefined') {
    return { modalOpenCount: 0, adStats: {} };
  }

  try {
    const stored = localStorage.getItem(AD_STATS_KEY);
    if (stored) {
      return JSON.parse(stored);
    }
  } catch (error) {
  }

  return { modalOpenCount: 0, adStats: {} };
}

// 保存广告统计数据
function saveAdStats(stats: AdStats): void {
  if (typeof window === 'undefined') return;

  try {
    localStorage.setItem(AD_STATS_KEY, JSON.stringify(stats));
  } catch (error) {
  }
}

// 智能统计更新 - 考虑时间衰减和准确性
function updateAdStats(adId: number, type: 'show' | 'click'): void {
  const stats = getAdStats();

  if (!stats.adStats[adId]) {
    stats.adStats[adId] = {
      showCount: 0,
      clickCount: 0,
      showRate: 0,
      clickRate: 0,
    };
  }

  const adStat = stats.adStats[adId];

  if (type === 'show') {
    adStat.showCount++;

    // 计算展示率：该广告展示次数 / 总模态框打开次数
    adStat.showRate = stats.modalOpenCount > 0
      ? adStat.showCount / stats.modalOpenCount
      : 0;

  } else if (type === 'click') {
    adStat.clickCount++;

    // 计算点击率：点击次数 / 展示次数
    adStat.clickRate = adStat.showCount > 0
      ? adStat.clickCount / adStat.showCount
      : 0;
  }

  // 应用时间衰减因子（可选）
  // 如果广告展示次数过多，轻微降低历史数据权重
  if (adStat.showCount > 100) {
    const decayFactor = 0.99;
    adStat.showRate *= decayFactor;
    adStat.clickRate *= decayFactor;
  }

  saveAdStats(stats);
}

// 记录模态框打开
export function recordModalOpen(): void {
  const stats = getAdStats();
  stats.modalOpenCount++;
  saveAdStats(stats);
}

// 记录广告展示
export function recordAdShow(adId: number): void {
  updateAdStats(adId, 'show');
}

// 记录广告点击
export function recordAdClick(adId: number): void {
  updateAdStats(adId, 'click');
}

// 选择Direct link广告 - 用于模态框点击时的跳转
export function selectDirectLinkAd(): AdUnit | null {
  return selectAd('direct-link');
}

// 记录Direct link点击（单独统计）
export function recordDirectLinkClick(adId: number): void {
  // 可以使用相同的统计逻辑，或者如果需要单独统计可以扩展
  recordAdClick(adId);
}

// 处理模态框数据卡片点击 - 同时打开Direct link和原tracking URL
export function handleModalCardClick(trackingUrl?: string, originUrl?: string, siteUrl?: string): void {
  // 1. 先选择并打开Direct link广告
  const directLinkAd = selectDirectLinkAd();
  if (directLinkAd) {
    // Direct link的html字段包含要跳转的URL
    const directLinkUrl = directLinkAd.html.trim();
    if (directLinkUrl.startsWith('http')) {
      window.open(directLinkUrl, '_blank');
      recordDirectLinkClick(directLinkAd.id);
    }
  }

  // 2. 然后打开原来的tracking URL (稍微延迟确保第二个标签页成为活动标签页)
  setTimeout(() => {
    if (trackingUrl) {
      window.open(trackingUrl, '_blank');
    } else if (originUrl) {
      window.open(originUrl, '_blank');
    } else if (siteUrl) {
      window.open(`https://${siteUrl}`, '_blank');
    }
  }, 50); // 50ms延迟，确保Direct link先打开，但tracking URL的标签页成为活动标签页
}

// 智能广告选择算法 - 考虑所有边界情况 (暂时未使用，但保留完整逻辑)
/*
function calculateAdScore(ad: AdUnit, stats: AdStats, isTopPosition: boolean): number {
  const adStat = stats.adStats[ad.id] || {
    showCount: 0,
    clickCount: 0,
    showRate: 0,
    clickRate: 0,
  };

  // 基础分数从优先级开始
  let score = ad.priority;

  // === 边界情况1: 数据不足时的处理 ===
  const hasEnoughData = adStat.showCount >= 10; // 至少10次展示才有可靠数据
  const totalModalOpens = stats.modalOpenCount || 1; // 避免除零

  // === 1. AdSense优先策略 ===
  if (ad.adType === 'adsense') {
    score += 100; // 基础高分

    // 位置加成
    if (isTopPosition) {
      score += 50;
    } else {
      score += 30;
    }
  } else {
    // 其他类型广告作为填充
    score += 20;
  }

  // === 2. 点击率智能控制（考虑数据充分性）===
  const currentClickRate = adStat.clickRate;
  const maxClickRate = ad.maxClickRate;

  if (hasEnoughData) {
    // 数据充分时，严格控制
    const clickRateRatio = maxClickRate > 0 ? currentClickRate / maxClickRate : 0;

    if (currentClickRate >= maxClickRate) {
      // 超限严重惩罚
      score -= (ad.adType === 'adsense' ? 500 : 200);
    } else if (clickRateRatio > 0.9) {
      // 接近上限，开始降权
      const penalty = (clickRateRatio - 0.9) * 10 * (ad.adType === 'adsense' ? 300 : 100);
      score -= penalty;
    } else if (clickRateRatio > 0.7) {
      // 较高但安全，轻微降权
      const penalty = (clickRateRatio - 0.7) * 5 * (ad.adType === 'adsense' ? 100 : 50);
      score -= penalty;
    } else {
      // 安全范围，给予奖励
      const bonus = (0.7 - clickRateRatio) * (ad.adType === 'adsense' ? 80 : 40);
      score += bonus;
    }
  } else {
    // === 边界情况2: 数据不足时的宽松策略 ===
    if (currentClickRate >= maxClickRate * 1.5) {
      // 只有严重超限才惩罚（1.5倍容忍度）
      score -= (ad.adType === 'adsense' ? 200 : 100);
    } else if (currentClickRate >= maxClickRate) {
      // 轻微超限，小幅惩罚
      score -= (ad.adType === 'adsense' ? 50 : 25);
    } else {
      // 数据收集期，给予鼓励
      score += (ad.adType === 'adsense' ? 50 : 25);
    }
  }

  // === 3. 展示率智能控制（考虑数据充分性）===
  const currentShowRate = adStat.showRate;
  const maxShowRate = ad.maxShowRate;

  if (hasEnoughData && totalModalOpens >= 20) {
    // 数据充分时，严格控制展示率
    const showRateRatio = maxShowRate > 0 ? currentShowRate / maxShowRate : 0;

    if (currentShowRate >= maxShowRate) {
      score -= (ad.adType === 'adsense' ? 300 : 150);
    } else if (showRateRatio > 0.8) {
      const penalty = (showRateRatio - 0.8) * 5 * (ad.adType === 'adsense' ? 150 : 75);
      score -= penalty;
    } else {
      const bonus = (0.8 - showRateRatio) * (ad.adType === 'adsense' ? 60 : 30);
      score += bonus;
    }
  } else {
    // === 边界情况3: 展示率数据不足时的宽松策略 ===
    if (currentShowRate >= maxShowRate * 1.3) {
      // 只有明显超限才惩罚
      score -= (ad.adType === 'adsense' ? 100 : 50);
    } else {
      // 数据收集期，鼓励展示
      score += (ad.adType === 'adsense' ? 30 : 15);
    }
  }

  // === 4. 新广告冷启动（边界情况4）===
  if (adStat.showCount < 10) {
    // 新广告必须有机会收集数据
    const newAdBonus = (10 - adStat.showCount) * (ad.adType === 'adsense' ? 30 : 20);
    score += newAdBonus;
  }

  // === 5. 零数据边界情况 ===
  if (adStat.showCount === 0) {
    // 从未展示过的广告，必须给机会
    score += (ad.adType === 'adsense' ? 200 : 100);
  }

  // === 6. 填充策略优化 ===
  if (ad.adType !== 'adsense') {
    // 检查AdSense是否真的受限（需要足够数据才判断）
    const adsenseAds = Object.entries(stats.adStats).filter(([, stat]) => {
      return stat.showCount >= 10 && (stat.clickRate >= 0.25 || stat.showRate >= 0.5);
    });

    if (adsenseAds.length > 0) {
      score += 150; // 填充奖励
    }
  }

  // === 7. 平衡性调整（边界情况5）===
  const totalShows = Object.values(stats.adStats).reduce((sum, stat) => sum + stat.showCount, 0);
  if (totalShows >= 20) { // 只有足够数据时才平衡
    const adShowRatio = adStat.showCount / totalShows;

    if (adShowRatio > 0.8) {
      score -= 150; // 过度展示
    } else if (adShowRatio < 0.1 && adStat.showCount > 0) {
      score += 100; // 展示不足
    }
  }

  // === 边界情况6: 确保最小可用分数 ===
  // 即使在最坏情况下，也要保证广告有基本可选性
  const minScore = ad.adType === 'adsense' ? 50 : 20;
  return Math.max(minScore, score);
}
*/

// 智能广告选择 - 确保有广告返回
export function selectAd(position: 'modal' | 'sidebar' | 'in-article' | 'direct-link' = 'modal'): AdUnit | null {
  const config = getConfig();
  const adUnits = config.adUnits;

  // 筛选符合条件的广告
  const availableAds = adUnits.filter(ad =>
    ad.is_active &&
    ad.position === position
  );

  if (availableAds.length === 0) {
    return null;
  }

  // 如果只有一个广告，直接返回
  if (availableAds.length === 1) {
    return availableAds[0];
  }

  // 智能选择算法 - 比随机更智能，但确保必选
  const stats = getAdStats();

  // 计算每个广告的智能分数
  const adScores = availableAds.map(ad => {
    const adStat = stats.adStats[ad.id] || {
      showCount: 0,
      clickCount: 0,
      showRate: 0,
      clickRate: 0,
    };

    let score = ad.priority; // 基础分数

    // AdSense优先策略
    if (ad.adType === 'adsense') {
      score += 100;
    }

    // 简单的点击率控制
    if (adStat.showCount >= 5) { // 有足够数据时才控制
      const clickRateRatio = adStat.clickRate / ad.maxClickRate;
      if (clickRateRatio >= 1.0) {
        score *= 0.3; // 超限降权，但不归零
      } else if (clickRateRatio > 0.8) {
        score *= 0.7; // 接近上限降权
      }
    }

    // 确保最小分数 - 比随机更智能的兜底
    return Math.max(score * 0.1, 10); // 最差也有10分
  });

  // 加权随机选择 - 确保必选
  const totalScore = adScores.reduce((sum, score) => sum + score, 0);

  // 兜底1：如果总分为0（不可能，但保险）
  if (totalScore <= 0) {
    return availableAds[Math.floor(Math.random() * availableAds.length)];
  }

  // 加权随机选择
  const randomValue = Math.random() * totalScore;
  let cumulativeScore = 0;

  for (let i = 0; i < availableAds.length; i++) {
    cumulativeScore += adScores[i];
    if (cumulativeScore >= randomValue) {
      return availableAds[i];
    }
  }

  // 兜底2：理论上不会到这里，但确保必选
  return availableAds[Math.floor(Math.random() * availableAds.length)];
}

// 检查广告是否应该在上半部分显示
export function shouldShowAdOnTop(): boolean {
  return Math.random() < 0.7; // 70%概率在上半部分
}

// 获取广告统计信息（用于调试）
export function getAdStatistics(): AdStats {
  return getAdStats();
}

// 重置广告统计（用于测试）
export function resetAdStatistics(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(AD_STATS_KEY);
  }
}
