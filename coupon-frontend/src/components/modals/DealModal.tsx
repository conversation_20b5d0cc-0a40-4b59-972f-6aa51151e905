import React, { useState, useEffect, useCallback } from 'react';
import { Deal, Brand } from '@/types/api';
import { formatDate, getImageUrl } from '@/lib/utils';
import { selectAd, recordModalOpen, recordAdShow, recordAdClick, shouldShowAdOnTop, handleModalCardClick } from '@/lib/adManager';
import { AdUnit } from '@/config/types';
import AdRenderer from '@/components/ui/AdRenderer';
import { useThemeClasses } from '@/config/theme';

interface DealModalProps {
  isOpen: boolean;
  onClose: () => void;
  deal: Deal | null;
  brand: Brand | null;
}

const DealModal: React.FC<DealModalProps> = ({ isOpen, onClose, deal, brand }) => {
  const theme = useThemeClasses();
  const [selectedAd, setSelectedAd] = useState<AdUnit | null>(null);
  const [adOnTop, setAdOnTop] = useState(false);
  const [adKey, setAdKey] = useState(0); // 用于强制广告重新渲染的key

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';

      // 记录模态框打开
      recordModalOpen();

      // 选择广告
      const ad = selectAd('modal');
      setSelectedAd(ad);

      // 决定广告位置
      setAdOnTop(shouldShowAdOnTop());

      // 设置广告key，确保每次打开模态框都是新的广告实例
      setAdKey(Date.now());

      // 记录广告展示
      if (ad) {
        recordAdShow(ad.id);
      }
    } else {
      document.body.style.overflow = 'unset';
      // 清理广告状态，防止状态残留
      setSelectedAd(null);
      setAdOnTop(false);
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // 渲染优惠内容 - 紧凑布局，控制信息占比
  const renderDealContent = () => (
    <div className="bg-white rounded-xl p-6">
      <div className="bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-start gap-4">
          {/* 左侧：Deal图片 - 正方形且较小 */}
          {deal?.img && (
            <div className="w-24 h-24 rounded-lg overflow-hidden flex-shrink-0">
              <img
                src={getImageUrl(deal.img)}
                alt={deal.title || 'Deal'}
                className="w-full h-full object-cover"
              />
            </div>
          )}

          {/* 右侧：Deal信息 */}
          <div className="flex-1 min-w-0">
            {/* 品牌名称 */}
            {brand && (
              <div className="text-sm font-medium text-gray-600 mb-1">
                {brand.name}
              </div>
            )}

            {/* Deal标题 */}
            <h2 className="text-lg font-bold text-gray-900 mb-2 line-clamp-2">
              {deal?.title || 'Special Deal'}
            </h2>

            {/* 折扣标签 */}
            {deal?.discount && (
              <div className="mb-2">
                <span
                  className="inline-block px-3 py-1 rounded-full text-sm font-bold"
                  style={theme.dealModal.discountBadge.style}
                >
                  {deal.discount}
                </span>
              </div>
            )}

            {/* 描述 - 限制行数 */}
            {deal?.description && (
              <div className="text-gray-700 text-sm mb-3 line-clamp-3">
                {deal.description}
              </div>
            )}

            {/* 过期时间 */}
            <div className="text-xs text-gray-500 mb-4">
              {deal?.end_date ? (
                <span>Expires: {formatDate(deal.end_date)}</span>
              ) : (
                <span className="text-green-600 font-semibold">No Expiry</span>
              )}
            </div>
          </div>
        </div>

        {/* Get Deal按钮 */}
        <button
          onClick={handleVisitStore}
          className="w-full px-6 py-3 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2 hover:scale-105 mt-4"
          style={theme.dealModal.actionButton.style}
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
          </svg>
          Get Deal
        </button>
      </div>
    </div>
  );

  const handleVisitStore = () => {
    // 使用新的处理函数同时打开Direct link和原tracking URL
    handleModalCardClick(brand?.tracking_url, brand?.origin_url, brand?.site_url);
  };

  const handleAdClick = useCallback(() => {
    if (selectedAd) {
      recordAdClick(selectedAd.id);
    }
  }, [selectedAd]);

  // 只能通过关闭按钮关闭
  const handleClose = () => {
    onClose();
  };

  // 渲染广告组件 - 始终显示广告区域
  const renderAd = useCallback(() => {
    if (!selectedAd || !isOpen) {
      // 即使没有广告，也要显示占位区域
      return (
        <div
          className="w-full h-full min-h-[300px] flex items-center justify-center"
          style={{ width: '100%', height: '100%' }}
        >
          {/* 空白占位，保持布局 */}
        </div>
      );
    }

    return (
      <div key={`ad-${selectedAd.id}-${adKey}`} style={{ width: '100%', height: '100%' }}>
        <AdRenderer
          ad={selectedAd}
          onAdClick={handleAdClick}
          className="w-full h-full"
        />
      </div>
    );
  }, [selectedAd, isOpen, handleAdClick, adKey]);

  if (!isOpen || !deal) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop - 不可点击关闭 */}
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" />

      {/* Modal */}
      <div className="relative bg-white rounded-3xl shadow-2xl max-w-5xl w-full max-h-[95vh] overflow-hidden">
        {/* Close Button - 极小的点击区域，减少意外关闭 */}
        <button
          onClick={handleClose}
          className="absolute top-6 right-6 z-10 transition-all duration-300 opacity-20 hover:opacity-50 group"
          style={{
            cursor: 'default',
            padding: '2px', // 极小的padding
            width: '16px',  // 固定小尺寸
            height: '16px'
          }}
        >
          <svg
            className="w-3 h-3 text-gray-400 group-hover:text-gray-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            strokeDasharray="4,3"
            strokeWidth="2"
          >
            <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        <div className="p-8 max-h-[95vh] overflow-y-auto">
          {/* 固定布局：数据占一半，广告占一半 */}
          <div className="flex flex-col h-full min-h-[600px]">
            {adOnTop ? (
              <>
                {/* 广告在上方 - 占一半 */}
                <div className="flex-1 min-h-[300px] mb-6">
                  {renderAd()}
                </div>
                {/* Deal内容在下方 - 占一半 */}
                <div className="flex-1 min-h-[300px]">
                  {renderDealContent()}
                </div>
              </>
            ) : (
              <>
                {/* Deal内容在上方 - 占一半 */}
                <div className="flex-1 min-h-[300px] mb-6">
                  {renderDealContent()}
                </div>
                {/* 广告在下方 - 占一半 */}
                <div className="flex-1 min-h-[300px]">
                  {renderAd()}
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DealModal;
