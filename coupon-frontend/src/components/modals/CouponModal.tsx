import React, { useState, useEffect, useCallback } from 'react';
import { Coupon, Brand } from '@/types/api';
import { formatDate } from '@/lib/utils';
import { selectAd, recordModalOpen, recordAdShow, recordAdClick, shouldShowAdOnTop, handleModalCardClick } from '@/lib/adManager';
import { AdUnit } from '@/config/types';
import AdRenderer from '@/components/ui/AdRenderer';
import { useThemeClasses } from '@/config/theme';

interface CouponModalProps {
  isOpen: boolean;
  onClose: () => void;
  coupon: Coupon | null;
  brand: Brand | null;
}

// 独立的复制按钮组件，避免状态变化影响父组件
const CopyButton: React.FC<{ onCopy: () => void; theme: any }> = ({ onCopy, theme }) => {
  const [copied, setCopied] = useState(false);

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
    onCopy();
  };

  return (
    <button
      onClick={handleClick}
      className="px-6 py-3 rounded-lg font-semibold transition-colors flex items-center gap-2 hover:scale-105"
      style={theme.couponModal.copyButton.style}
    >
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
      </svg>
      {copied ? 'Copied!' : 'Copy'}
    </button>
  );
};

const CouponModal: React.FC<CouponModalProps> = ({ isOpen, onClose, coupon, brand }) => {
  const theme = useThemeClasses();
  const [selectedAd, setSelectedAd] = useState<AdUnit | null>(null);
  const [adOnTop, setAdOnTop] = useState(false);
  const [adKey, setAdKey] = useState(0); // 用于强制广告重新渲染的key

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';

      // 记录模态框打开
      recordModalOpen();

      // 选择广告
      const ad = selectAd('modal');
      setSelectedAd(ad);

      // 决定广告位置
      setAdOnTop(shouldShowAdOnTop());

      // 设置广告key，确保每次打开模态框都是新的广告实例
      setAdKey(Date.now());

      // 记录广告展示
      if (ad) {
        recordAdShow(ad.id);
      }
    } else {
      document.body.style.overflow = 'unset';
      // 清理广告状态，防止状态残留
      setSelectedAd(null);
      setAdOnTop(false);
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const handleCopy = useCallback(async () => {
    if (coupon?.code) {
      try {
        // 复制优惠券代码
        await navigator.clipboard.writeText(coupon.code);

        // 延迟跳转，避免影响广告显示
        setTimeout(() => {
          handleModalCardClick(brand?.tracking_url, brand?.origin_url);
        }, 100);
      } catch (err) {
        // 即使复制失败，也尝试跳转
        setTimeout(() => {
          handleModalCardClick(brand?.tracking_url, brand?.origin_url);
        }, 100);
      }
    }
  }, [coupon?.code, brand?.tracking_url, brand?.origin_url]);

  // handleVisitStore removed as it's not used in this modal

  const handleAdClick = useCallback(() => {
    if (selectedAd) {
      recordAdClick(selectedAd.id);
    }
  }, [selectedAd]);

  // 只能通过关闭按钮关闭
  const handleClose = () => {
    onClose();
  };

  // 渲染广告组件 - 始终显示广告区域
  const renderAd = useCallback(() => {
    if (!selectedAd || !isOpen) {
      // 即使没有广告，也要显示占位区域
      return (
        <div
          className="w-full h-full min-h-[300px] flex items-center justify-center"
          style={{ width: '100%', height: '100%' }}
        >
          {/* 空白占位，保持布局 */}
        </div>
      );
    }

    return (
      <div key={`ad-${selectedAd.id}-${adKey}`} style={{ width: '100%', height: '100%' }}>
        <AdRenderer
          ad={selectedAd}
          onAdClick={handleAdClick}
          className="w-full h-full"
        />
      </div>
    );
  }, [selectedAd, isOpen, handleAdClick, adKey]);

  // 渲染优惠券内容 - 紧凑长条布局，整个卡片可点击
  const renderCouponContent = () => (
    <div className="bg-white rounded-xl p-6">
      <div
        className="flex items-center gap-6 bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-4 cursor-pointer"
        onClick={handleCopy}
      >
        {/* 左侧：品牌Logo */}
        <div className="flex-shrink-0">
          {brand?.logo ? (
            <img
              src={brand.logo}
              alt={brand.name}
              className="w-16 h-16 rounded-lg object-cover"
            />
          ) : (
            <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-blue-500 rounded-lg flex items-center justify-center">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
              </svg>
            </div>
          )}
        </div>

        {/* 中间：品牌信息、折扣、名称、过期时间 */}
        <div className="flex-1 min-w-0">
          {/* 品牌名称 */}
          {brand && (
            <div className="text-base font-semibold text-gray-800 truncate">
              {brand.name}
            </div>
          )}

          {/* 优惠券名称 */}
          <div className="text-xl font-bold text-gray-900 truncate mb-2">
            {coupon?.name}
          </div>

          <div className="flex items-center gap-4 text-sm">
            {/* 折扣信息 */}
            {coupon?.discount && (
              <span
                className="px-2 py-1 rounded-full text-xs font-semibold"
                style={theme.couponModal.discountBadge.style}
              >
                {coupon.discount}
              </span>
            )}

            {/* 过期时间 */}
            {coupon?.end_date ? (
              <span className="text-gray-600">
                Expires: {formatDate(coupon.end_date)}
              </span>
            ) : (
              <span className="text-green-600 font-semibold">
                No Expiry
              </span>
            )}
          </div>
        </div>

        {/* 右侧：优惠券代码和复制按钮 */}
        {coupon?.code && (
          <div className="flex-shrink-0 flex items-center gap-4">
            <div className="text-center">
              <div className="text-sm text-gray-500 mb-2">Code</div>
              <code className="text-xl font-mono font-bold text-green-700 bg-white px-4 py-2 rounded border-2 border-dashed border-green-300">
                {coupon.code}
              </code>
            </div>
            <CopyButton onCopy={handleCopy} theme={theme} />
          </div>
        )}
      </div>

    </div>
  );

  if (!isOpen || !coupon) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop - 不可点击关闭 */}
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" />

      {/* Modal */}
      <div className="relative bg-white rounded-3xl shadow-2xl max-w-5xl w-full max-h-[95vh] overflow-hidden">
        {/* Close Button - 极小的点击区域，减少意外关闭 */}
        <button
          onClick={handleClose}
          className="absolute top-6 right-6 z-10 transition-all duration-300 opacity-20 hover:opacity-50 group"
          style={{
            cursor: 'default',
            padding: '2px', // 极小的padding
            width: '16px',  // 固定小尺寸
            height: '16px'
          }}
        >
          <svg
            className="w-3 h-3 text-gray-400 group-hover:text-gray-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            strokeDasharray="4,3"
            strokeWidth="2"
          >
            <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        <div className="p-8 max-h-[95vh] overflow-y-auto">
          {/* 固定布局：数据占一半，广告占一半 */}
          <div className="flex flex-col h-full min-h-[600px]">
            {adOnTop ? (
              <>
                {/* 广告在上方 - 占一半 */}
                <div className="flex-1 min-h-[300px] mb-6">
                  {renderAd()}
                </div>
                {/* 优惠券内容在下方 - 占一半 */}
                <div className="flex-1 min-h-[300px]">
                  {renderCouponContent()}
                </div>
              </>
            ) : (
              <>
                {/* 优惠券内容在上方 - 占一半 */}
                <div className="flex-1 min-h-[300px] mb-6">
                  {renderCouponContent()}
                </div>
                {/* 广告在下方 - 占一半 */}
                <div className="flex-1 min-h-[300px]">
                  {renderAd()}
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CouponModal;
