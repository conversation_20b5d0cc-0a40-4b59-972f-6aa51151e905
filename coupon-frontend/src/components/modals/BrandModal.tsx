import React, { useState, useEffect, useCallback } from 'react';
import { Brand, Coupon, Deal } from '@/types/api';
import { formatDate } from '@/lib/utils';
import { selectAd, recordModalOpen, recordAdShow, recordAdClick, shouldShowAdOnTop, handleModalCardClick } from '@/lib/adManager';
import { AdUnit } from '@/config/types';
import AdRenderer from '@/components/ui/AdRenderer';
import apiService from '@/services/api';

interface BrandModalProps {
  isOpen: boolean;
  onClose: () => void;
  brand: Brand | null;
}

// 独立的复制按钮组件，避免状态变化影响父组件
const CopyButton: React.FC<{ onCopy: () => void; text: string }> = ({ onCopy, text }) => {
  const [copied, setCopied] = useState(false);

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
    onCopy();
  };

  return (
    <button
      onClick={handleClick}
      className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors flex items-center gap-2 hover:scale-105"
    >
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
      </svg>
      {copied ? 'Copied!' : text}
    </button>
  );
};

const BrandModal: React.FC<BrandModalProps> = ({ isOpen, onClose, brand }) => {
  const [selectedAd, setSelectedAd] = useState<AdUnit | null>(null);
  const [adOnTop, setAdOnTop] = useState(false);
  const [adKey, setAdKey] = useState(0); // 用于强制广告重新渲染的key
  const [randomCoupon, setRandomCoupon] = useState<Coupon | null>(null);
  const [randomDeal, setRandomDeal] = useState<Deal | null>(null);
  const [brandWithDetails, setBrandWithDetails] = useState<Brand | null>(null);

  useEffect(() => {
    if (isOpen && brand) {
      document.body.style.overflow = 'hidden';

      // 记录模态框打开
      recordModalOpen();

      // 选择广告
      const ad = selectAd('modal');
      setSelectedAd(ad);

      // 决定广告位置
      setAdOnTop(shouldShowAdOnTop());

      // 设置广告key，确保每次打开模态框都是新的广告实例
      setAdKey(Date.now());

      // 记录广告展示
      if (ad) {
        recordAdShow(ad.id);
      }

      // 获取品牌详细信息（包含coupons和deals）
      const fetchBrandDetails = async () => {
        try {
          let brandDetails = brand;
          
          // 如果当前brand没有coupons和deals数据，从API获取详细信息
          if (!brand.coupons && !brand.deals) {
            const response = await apiService.getBrandById(brand.id);
            if (response) {
              brandDetails = response;
              setBrandWithDetails(response);
            } else {
              setBrandWithDetails(brand);
            }
          } else {
            setBrandWithDetails(brand);
          }

          // 随机选择优惠券和deals
          if (brandDetails.coupons && brandDetails.coupons.length > 0) {
            const randomIndex = Math.floor(Math.random() * brandDetails.coupons.length);
            setRandomCoupon(brandDetails.coupons[randomIndex]);
          } else {
            setRandomCoupon(null);
          }

          if (brandDetails.deals && brandDetails.deals.length > 0) {
            const randomIndex = Math.floor(Math.random() * brandDetails.deals.length);
            setRandomDeal(brandDetails.deals[randomIndex]);
          } else {
            setRandomDeal(null);
          }
        } catch (error) {
          console.error('Failed to fetch brand details:', error);
          setBrandWithDetails(brand);
          setRandomCoupon(null);
          setRandomDeal(null);
        }
      };

      fetchBrandDetails();
    } else {
      document.body.style.overflow = 'unset';
      // 清理广告状态，防止状态残留
      setSelectedAd(null);
      setAdOnTop(false);
      setRandomCoupon(null);
      setRandomDeal(null);
      setBrandWithDetails(null);
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, brand]);

  const handleCopyCoupon = useCallback(async () => {
    if (randomCoupon?.code) {
      try {
        // 复制优惠券代码
        await navigator.clipboard.writeText(randomCoupon.code);

        // 延迟跳转，避免影响广告显示
        setTimeout(() => {
          handleModalCardClick(
            brandWithDetails?.tracking_url || brand?.tracking_url,
            brandWithDetails?.origin_url || brand?.origin_url
          );
        }, 100);
      } catch (err) {
        // 即使复制失败，也尝试跳转
        setTimeout(() => {
          handleModalCardClick(
            brandWithDetails?.tracking_url || brand?.tracking_url,
            brandWithDetails?.origin_url || brand?.origin_url
          );
        }, 100);
      }
    }
  }, [randomCoupon?.code, brandWithDetails, brand]);

  const handleVisitDeal = useCallback(() => {
    // 使用新的处理函数同时打开Direct link和原tracking URL
    handleModalCardClick(
      randomDeal?.tracking_url || brandWithDetails?.tracking_url || brand?.tracking_url,
      randomDeal?.origin_url || brandWithDetails?.origin_url || brand?.origin_url
    );
  }, [randomDeal, brandWithDetails, brand]);

  const handleAdClick = useCallback(() => {
    if (selectedAd) {
      recordAdClick(selectedAd.id);
    }
  }, [selectedAd]);

  // 只能通过关闭按钮关闭
  const handleClose = () => {
    onClose();
  };

  // 渲染广告组件 - 始终显示广告区域
  const renderAd = useCallback(() => {
    if (!selectedAd || !isOpen) {
      // 即使没有广告，也要显示占位区域
      return (
        <div
          className="w-full h-full min-h-[300px] flex items-center justify-center"
          style={{ width: '100%', height: '100%' }}
        >
          {/* 空白占位，保持布局 */}
        </div>
      );
    }

    return (
      <div key={`ad-${selectedAd.id}-${adKey}`} style={{ width: '100%', height: '100%' }}>
        <AdRenderer
          ad={selectedAd}
          onAdClick={handleAdClick}
          className="w-full h-full"
        />
      </div>
    );
  }, [selectedAd, isOpen, handleAdClick, adKey]);

  // 渲染品牌内容
  const renderBrandContent = () => {
    const currentBrand = brandWithDetails || brand;
    const hasCouponsOrDeals = (currentBrand?.total_coupons || 0) + (currentBrand?.total_deals || 0) > 0;
    const hasActualCouponsOrDeals = randomCoupon || randomDeal;
    
    // 如果没有实际的优惠券和交易数据，或者总数为0，显示品牌信息
    if (!hasCouponsOrDeals || !hasActualCouponsOrDeals) {
      return (
        <div className="bg-white rounded-xl p-6">
          <div
            className="flex items-center gap-6 bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6 cursor-pointer hover:bg-gradient-to-r hover:from-blue-100 hover:to-purple-100 transition-colors"
            onClick={() => {
              if (currentBrand?.tracking_url) {
                window.open(currentBrand.tracking_url, '_blank');
              } else if (currentBrand?.origin_url) {
                window.open(currentBrand.origin_url, '_blank');
              }
            }}
          >
            <div className="flex-shrink-0">
              {currentBrand?.logo ? (
                <img
                  src={currentBrand.logo}
                  alt={currentBrand.name}
                  className="w-20 h-20 rounded-lg object-cover"
                />
              ) : (
                <div className="w-20 h-20 bg-gradient-to-br from-blue-400 to-purple-500 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-2xl">🏪</span>
                </div>
              )}
            </div>
            <div className="flex-1">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">{currentBrand?.name}</h2>
              <p className="text-gray-600 text-sm mb-3">{currentBrand?.description}</p>
              <div className="flex items-center gap-4 text-sm">
                {hasCouponsOrDeals ? (
                  <div className="flex gap-2">
                    {(currentBrand?.total_coupons || 0) > 0 && (
                      <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full font-semibold">
                        {currentBrand?.total_coupons} Coupons
                      </span>
                    )}
                    {(currentBrand?.total_deals || 0) > 0 && (
                      <span className="bg-orange-100 text-orange-700 px-2 py-1 rounded-full font-semibold">
                        {currentBrand?.total_deals} Deals
                      </span>
                    )}
                  </div>
                ) : (
                  <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full font-semibold">
                    Visit Store
                  </span>
                )}
              </div>
            </div>
            <div className="flex-shrink-0">
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors hover:scale-105">
                Visit {currentBrand?.name}
              </button>
            </div>
          </div>
        </div>
      );
    }

    // 有coupon或deal时，显示优惠信息
    return (
      <div className="bg-white rounded-xl p-6">
        {/* 随机优惠券 */}
        {randomCoupon && (
          <div className="mb-6">
            <div
              className="flex items-center gap-6 bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-4 cursor-pointer"
              onClick={handleCopyCoupon}
            >
              <div className="flex-shrink-0">
                {currentBrand?.logo ? (
                  <img
                    src={currentBrand.logo}
                    alt={currentBrand.name}
                    className="w-16 h-16 rounded-lg object-cover"
                  />
                ) : (
                  <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-blue-500 rounded-lg flex items-center justify-center">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                    </svg>
                  </div>
                )}
              </div>
              
              <div className="flex-1 min-w-0">
                {currentBrand && (
                  <div className="text-base font-semibold text-gray-800 truncate">
                    {currentBrand.name}
                  </div>
                )}
                <div className="text-xl font-bold text-gray-900 truncate mb-2">
                  {randomCoupon.name}
                </div>
                <div className="flex items-center gap-4 text-sm">
                  {randomCoupon.discount && (
                    <span className="bg-red-100 text-red-700 px-2 py-1 rounded-full text-xs font-semibold">
                      {randomCoupon.discount}
                    </span>
                  )}
                  {randomCoupon.end_date ? (
                    <span className="text-gray-600">
                      Expires: {formatDate(randomCoupon.end_date)}
                    </span>
                  ) : (
                    <span className="text-green-600 font-semibold">
                      No Expiry
                    </span>
                  )}
                </div>
              </div>
              
              {randomCoupon.code && (
                <div className="flex-shrink-0 flex items-center gap-4">
                  <div className="text-center">
                    <div className="text-sm text-gray-500 mb-2">Code</div>
                    <code className="text-xl font-mono font-bold text-green-700 bg-white px-4 py-2 rounded border-2 border-dashed border-green-300">
                      {randomCoupon.code}
                    </code>
                  </div>
                  <CopyButton onCopy={handleCopyCoupon} text="Copy" />
                </div>
              )}
            </div>
          </div>
        )}

        {/* 随机Deal */}
        {randomDeal && (
          <div className="mb-6">
            <div
              className="flex items-center gap-6 bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200 rounded-lg p-4 cursor-pointer"
              onClick={handleVisitDeal}
            >
              <div className="flex-1 min-w-0">
                <div className="text-lg font-bold text-gray-900 truncate mb-2">
                  {randomDeal.title}
                </div>
                <div className="text-sm text-gray-600 mb-2 line-clamp-2">
                  {randomDeal.description}
                </div>
                <div className="flex items-center gap-4 text-sm">
                  {randomDeal.discount && (
                    <span className="bg-red-100 text-red-700 px-2 py-1 rounded-full text-xs font-semibold">
                      {randomDeal.discount}
                    </span>
                  )}
                  {randomDeal.is_hot_deal && (
                    <span className="bg-orange-100 text-orange-700 px-2 py-1 rounded-full text-xs font-semibold">
                      🔥 Hot Deal
                    </span>
                  )}
                  {randomDeal.end_date ? (
                    <span className="text-gray-600">
                      Expires: {formatDate(randomDeal.end_date)}
                    </span>
                  ) : (
                    <span className="text-green-600 font-semibold">
                      No Expiry
                    </span>
                  )}
                </div>
              </div>
              <div className="flex-shrink-0">
                <button className="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors hover:scale-105">
                  Visit Deal
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 如果没有显示任何优惠券或交易，显示品牌信息 */}
        {!randomCoupon && !randomDeal && (
          <div className="text-center py-8">
            <div className="flex justify-center mb-4">
              {currentBrand?.logo ? (
                <img
                  src={currentBrand.logo}
                  alt={currentBrand.name}
                  className="w-16 h-16 rounded-lg object-cover"
                />
              ) : (
                <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-500 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-xl">🏪</span>
                </div>
              )}
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">{currentBrand?.name}</h3>
            <p className="text-gray-600 mb-4">{currentBrand?.description}</p>
            <button
              onClick={() => {
                if (currentBrand?.tracking_url) {
                  window.open(currentBrand.tracking_url, '_blank');
                } else if (currentBrand?.origin_url) {
                  window.open(currentBrand.origin_url, '_blank');
                }
              }}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors hover:scale-105"
            >
              Visit {currentBrand?.name}
            </button>
          </div>
        )}
      </div>
    );
  };

  if (!isOpen || !brand) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop - 不可点击关闭 */}
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" />

      {/* Modal */}
      <div className="relative bg-white rounded-3xl shadow-2xl max-w-5xl w-full max-h-[95vh] overflow-hidden">
        {/* Close Button - 极小的点击区域，减少意外关闭 */}
        <button
          onClick={handleClose}
          className="absolute top-6 right-6 z-10 transition-all duration-300 opacity-20 hover:opacity-50 group"
          style={{
            cursor: 'default',
            padding: '2px', // 极小的padding
            width: '16px',  // 固定小尺寸
            height: '16px'
          }}
        >
          <svg
            className="w-3 h-3 text-gray-400 group-hover:text-gray-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            strokeDasharray="4,3"
            strokeWidth="2"
          >
            <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        <div className="p-8 max-h-[95vh] overflow-y-auto">
          {/* 固定布局：数据占一半，广告占一半 */}
          <div className="flex flex-col h-full min-h-[600px]">
            {adOnTop ? (
              <>
                {/* 广告在上方 - 占一半 */}
                <div className="flex-1 min-h-[300px] mb-6">
                  {renderAd()}
                </div>
                {/* 品牌内容在下方 - 占一半 */}
                <div className="flex-1 min-h-[300px]">
                  {renderBrandContent()}
                </div>
              </>
            ) : (
              <>
                {/* 品牌内容在上方 - 占一半 */}
                <div className="flex-1 min-h-[300px] mb-6">
                  {renderBrandContent()}
                </div>
                {/* 广告在下方 - 占一半 */}
                <div className="flex-1 min-h-[300px]">
                  {renderAd()}
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BrandModal;