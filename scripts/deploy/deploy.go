package main

import (
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/cheggaaa/pb/v3"
	"github.com/pkg/sftp"
	"github.com/sirupsen/logrus"
	"golang.org/x/crypto/ssh"
	"gopkg.in/yaml.v2"
)

var (
	log     = logrus.New()
	bufPool = sync.Pool{
		New: func() interface{} {
			return make([]byte, 32*1024) // 32KB buffer
		},
	}
)

// 配置结构体
type Config struct {
	SSH struct {
		Host         string `yaml:"host"`
		User         string `yaml:"user"`
		Password     string `yaml:"password"`
		Port         int    `yaml:"port"`
		FrontendPath string `yaml:"frontend_path"`
		BackendPaths struct {
			API     string `yaml:"api"`
			Migrate string `yaml:"migrate"`
			Task    string `yaml:"task"`
		} `yaml:"backend_paths"`
	} `yaml:"ssh"`

	Local struct {
		FrontendDir string `yaml:"frontend_dir"`
		BackendDir  string `yaml:"backend_dir"`
		BackupDir   string `yaml:"backup_dir"`
	} `yaml:"local"`

	Build struct {
		Frontend struct {
			BuildCommand string `yaml:"build_command"`
			DistDir      string `yaml:"dist_dir"`
		} `yaml:"frontend"`
		Backend struct {
			BuildCommand string   `yaml:"build_command"`
			BinDir       string   `yaml:"bin_dir"`
			Targets      []string `yaml:"targets"`
		} `yaml:"backend"`
	} `yaml:"build"`

	Retry struct {
		MaxAttempts int           `yaml:"max_attempts"`
		Delay       time.Duration `yaml:"delay"`
	} `yaml:"retry"`

	Upload struct {
		Workers    int `yaml:"workers"`
		BufferSize int `yaml:"buffer_size"`
	} `yaml:"upload"`
}

// Frontend 变量结构体
type FrontendVariables struct {
	SiteVariables struct {
		SiteName    string `json:"SITE_NAME"`
		SiteDomain  string `json:"SITE_DOMAIN"`
		SiteURL     string `json:"SITE_URL"`
		APIDomain   string `json:"API_DOMAIN"`
		Environment struct {
			APIBaseURLProduction  string `json:"API_BASE_URL_PRODUCTION"`
			APIBaseURLDevelopment string `json:"API_BASE_URL_DEVELOPMENT"`
			IsProduction          bool   `json:"IS_PRODUCTION"`
		} `json:"ENVIRONMENT"`
	} `json:"SITE_VARIABLES"`
}

var config Config

type FileInfo struct {
	Path    string
	Info    os.FileInfo
	RelPath string
	ModTime time.Time
}

func init() {
	// 配置日志
	log.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
		ForceColors:   true,
	})

	// 同时输出到控制台和文件
	logFile, err := os.OpenFile("deploy.log", os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		log.Fatal("无法创建日志文件: ", err)
	}

	multiWriter := io.MultiWriter(os.Stdout, logFile)
	log.SetOutput(multiWriter)

	// 加载配置文件
	configPath := "./deploy_config.yaml"
	if len(os.Args) > 1 && os.Args[1] != "" {
		configPath = os.Args[1]
	}

	configData, err := os.ReadFile(configPath)
	if err != nil {
		log.Fatal("无法读取配置文件: ", err)
	}

	if err := yaml.Unmarshal(configData, &config); err != nil {
		log.Fatal("无法解析配置文件: ", err)
	}

	log.Info("✅ 配置文件加载成功: ", configPath)
}

// 读取前端变量配置
func loadFrontendConfig() (*FrontendVariables, error) {
	variablesPath := filepath.Join(config.Local.FrontendDir, "src/config/site/variables.ts")

	// 创建临时目录编译TypeScript
	tempDir := filepath.Join(config.Local.FrontendDir, "temp-config")
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		return nil, fmt.Errorf("创建临时目录失败: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 编译TypeScript文件
	cmd := exec.Command("npx", "tsc", "--outDir", tempDir, "--target", "es2020",
		"--module", "es2020", "--moduleResolution", "node", "--skipLibCheck", "true",
		"src/config/site/variables.ts")
	cmd.Dir = config.Local.FrontendDir

	if err := cmd.Run(); err != nil {
		return nil, fmt.Errorf("编译variables.ts失败: %v", err)
	}

	// 这里简化处理，直接返回默认配置
	// 在实际项目中，你可能需要解析编译后的JavaScript文件
	return &FrontendVariables{}, nil
}

// 构建前端
func buildFrontend() error {
	log.Info("🏗️  开始构建前端...")

	cmd := exec.Command("npm", "run", "build:live")
	cmd.Dir = config.Local.FrontendDir
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("前端构建失败: %v", err)
	}

	// 验证构建产物
	distPath := filepath.Join(config.Local.FrontendDir, config.Build.Frontend.DistDir)
	if _, err := os.Stat(distPath); os.IsNotExist(err) {
		return fmt.Errorf("前端构建产物不存在: %s", distPath)
	}

	log.Info("✅ 前端构建完成")
	return nil
}

// 构建后端
func buildBackend() error {
	log.Info("🏗️  开始构建后端...")

	cmd := exec.Command("make", "build")
	cmd.Dir = config.Local.BackendDir
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("后端构建失败: %v", err)
	}

	// 验证构建产物
	binDir := filepath.Join(config.Local.BackendDir, config.Build.Backend.BinDir)
	for _, target := range config.Build.Backend.Targets {
		binPath := filepath.Join(binDir, target)
		if _, err := os.Stat(binPath); os.IsNotExist(err) {
			return fmt.Errorf("后端构建产物不存在: %s", binPath)
		}
	}

	log.Info("✅ 后端构建完成")
	return nil
}

// 上传前端文件
func uploadFrontend() error {
	log.Info("📤 开始上传前端文件...")

	localPath := filepath.Join(config.Local.FrontendDir, config.Build.Frontend.DistDir)
	remotePath := config.SSH.FrontendPath

	return uploadDirectory(localPath, remotePath, "前端")
}

// 上传后端文件
func uploadBackend() error {
	log.Info("📤 开始上传后端文件...")

	binDir := filepath.Join(config.Local.BackendDir, config.Build.Backend.BinDir)

	// 上传各个后端服务
	backendPaths := map[string]string{
		"api":     config.SSH.BackendPaths.API,
		"migrate": config.SSH.BackendPaths.Migrate,
		"task":    config.SSH.BackendPaths.Task,
	}

	for service, remotePath := range backendPaths {
		localBinary := filepath.Join(binDir, service)
		if err := uploadBinary(localBinary, remotePath, service); err != nil {
			return fmt.Errorf("上传%s服务失败: %v", service, err)
		}
		log.Infof("✅ %s服务上传完成", service)
	}

	return nil
}

// 上传单个二进制文件
func uploadBinary(localPath, remoteDir, serviceName string) error {
	// 连接SSH服务器
	sshClient, err := connectSSH()
	if err != nil {
		return fmt.Errorf("SSH连接失败: %v", err)
	}
	defer sshClient.Close()

	// 创建SFTP客户端
	sftpClient, err := sftp.NewClient(sshClient)
	if err != nil {
		return fmt.Errorf("SFTP客户端创建失败: %v", err)
	}
	defer sftpClient.Close()

	// 创建文件路径
	fileName := filepath.Base(localPath)
	tempRemotePath := filepath.Join(remoteDir, fileName+".tmp_"+time.Now().Format("20060102_150405"))
	finalRemotePath := filepath.Join(remoteDir, fileName)

	// 打开本地文件
	localFile, err := os.Open(localPath)
	if err != nil {
		return fmt.Errorf("打开本地文件失败: %v", err)
	}
	defer localFile.Close()

	// 获取文件信息
	fileInfo, err := localFile.Stat()
	if err != nil {
		return fmt.Errorf("获取文件信息失败: %v", err)
	}

	// 创建进度条
	bar := pb.Full.Start64(fileInfo.Size())
	bar.Set(pb.Bytes, true)
	bar.SetWidth(80)
	bar.SetRefreshRate(time.Second)
	bar.SetTemplateString(fmt.Sprintf(`上传%s: {{bar . }} {{percent . }} {{speed . }} {{counters . }}`, serviceName))

	// 创建临时远程文件
	remoteFile, err := sftpClient.Create(tempRemotePath)
	if err != nil {
		return fmt.Errorf("创建远程文件失败: %v", err)
	}
	defer remoteFile.Close()

	// 从内存池获取缓冲区
	buf := bufPool.Get().([]byte)
	defer bufPool.Put(buf)

	// 复制文件内容
	for {
		n, err := localFile.Read(buf)
		if n > 0 {
			_, writeErr := remoteFile.Write(buf[:n])
			if writeErr != nil {
				return fmt.Errorf("写入远程文件失败: %v", writeErr)
			}
			bar.Add(n)
		}
		if err != nil {
			if err != io.EOF {
				return fmt.Errorf("读取本地文件失败: %v", err)
			}
			break
		}
	}

	bar.Finish()

	// 设置文件权限
	if err := sftpClient.Chmod(tempRemotePath, 0755); err != nil {
		return fmt.Errorf("设置文件权限失败: %v", err)
	}

	// 备份当前文件（如果存在）
	backupPath := filepath.Join(remoteDir, fileName+".bak_"+time.Now().Format("20060102_150405"))
	if _, err := sftpClient.Stat(finalRemotePath); err == nil {
		if err := sftpClient.Rename(finalRemotePath, backupPath); err != nil {
			log.Warnf("创建备份失败: %v", err)
		} else {
			log.Infof("已备份现有文件: %s", backupPath)
		}
	}

	// 替换文件
	if err := sftpClient.Rename(tempRemotePath, finalRemotePath); err != nil {
		// 如果替换失败，尝试恢复备份
		if restoreErr := sftpClient.Rename(backupPath, finalRemotePath); restoreErr != nil {
			return fmt.Errorf("部署失败且无法恢复: %v", err)
		}
		return fmt.Errorf("部署失败，已恢复备份: %v", err)
	}

	// 删除备份文件（保留最近的备份）
	if backupPath != "" {
		time.Sleep(time.Second) // 等待一秒确保操作完成
		if err := sftpClient.Remove(backupPath); err != nil {
			log.Warnf("删除备份文件失败: %v", err)
		}
	}

	return nil
}

// 上传目录
func uploadDirectory(localPath, remotePath, description string) error {
	// 创建临时目录
	tempRemotePath := filepath.Join(filepath.Dir(remotePath), ".tmp_upload_"+time.Now().Format("20060102_150405"))

	// 连接SSH服务器
	sshClient, err := connectSSH()
	if err != nil {
		return fmt.Errorf("SSH连接失败: %v", err)
	}
	defer sshClient.Close()

	// 创建SFTP客户端
	sftpClient, err := sftp.NewClient(sshClient)
	if err != nil {
		return fmt.Errorf("SFTP客户端创建失败: %v", err)
	}
	defer sftpClient.Close()

	// 收集所有文件
	var files []FileInfo
	var totalBytes int64

	err = filepath.Walk(localPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() {
			relPath, err := filepath.Rel(localPath, path)
			if err != nil {
				return err
			}

			files = append(files, FileInfo{
				Path:    path,
				Info:    info,
				RelPath: relPath,
				ModTime: info.ModTime(),
			})
			totalBytes += info.Size()
		}
		return nil
	})

	if err != nil {
		return fmt.Errorf("收集文件信息失败: %v", err)
	}

	// 创建进度条
	bar := pb.Full.Start64(totalBytes)
	bar.Set(pb.Bytes, true)
	bar.SetWidth(80)
	bar.SetRefreshRate(time.Second)
	bar.SetTemplateString(fmt.Sprintf(`上传%s: {{bar . }} {{percent . }} {{speed . }} {{counters . }}`, description))

	var uploadedBytes int64
	var mu sync.Mutex

	// 创建工作池
	jobs := make(chan FileInfo, len(files))
	errors := make(chan error, len(files))
	wg := sync.WaitGroup{}

	// 启动工作协程
	workerCount := config.Upload.Workers
	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for file := range jobs {
				remoteFilePath := filepath.Join(tempRemotePath, file.RelPath)
				remoteDir := filepath.Dir(remoteFilePath)

				// 创建远程目录
				if err := sftpClient.MkdirAll(remoteDir); err != nil {
					errors <- fmt.Errorf("创建远程目录失败 %s: %v", remoteDir, err)
					continue
				}

				// 打开本地文件
				localFile, err := os.Open(file.Path)
				if err != nil {
					errors <- fmt.Errorf("打开本地文件失败 %s: %v", file.Path, err)
					continue
				}

				// 创建远程文件
				remoteFile, err := sftpClient.Create(remoteFilePath)
				if err != nil {
					localFile.Close()
					errors <- fmt.Errorf("创建远程文件失败 %s: %v", remoteFilePath, err)
					continue
				}

				// 从内存池获取缓冲区
				buf := bufPool.Get().([]byte)

				// 复制文件内容
				for {
					n, err := localFile.Read(buf)
					if n > 0 {
						_, writeErr := remoteFile.Write(buf[:n])
						if writeErr != nil {
							errors <- fmt.Errorf("写入远程文件失败 %s: %v", remoteFilePath, writeErr)
							break
						}
						mu.Lock()
						uploadedBytes += int64(n)
						bar.SetCurrent(uploadedBytes)
						mu.Unlock()
					}
					if err != nil {
						if err != io.EOF {
							errors <- fmt.Errorf("读取本地文件失败 %s: %v", file.Path, err)
						}
						break
					}
				}

				bufPool.Put(buf)
				localFile.Close()
				remoteFile.Close()

				// 设置文件权限和修改时间
				if err := sftpClient.Chmod(remoteFilePath, file.Info.Mode()); err != nil {
					log.Warnf("设置文件权限失败 %s: %v", remoteFilePath, err)
				}
			}
		}()
	}

	// 分发任务
	for _, file := range files {
		jobs <- file
	}
	close(jobs)

	// 等待所有工作完成
	wg.Wait()
	close(errors)
	bar.Finish()

	// 检查是否有错误
	for err := range errors {
		log.Error(err)
		return fmt.Errorf("上传过程中发生错误: %v", err)
	}

	// 备份当前目录
	backupPath := remotePath + ".bak_" + time.Now().Format("20060102_150405")
	if _, err := sftpClient.Stat(remotePath); err == nil {
		if err := sftpClient.Rename(remotePath, backupPath); err != nil {
			log.Warnf("创建备份失败: %v", err)
		} else {
			log.Infof("已备份现有目录: %s", backupPath)
		}
	}

	// 替换目录
	if err := sftpClient.Rename(tempRemotePath, remotePath); err != nil {
		// 如果替换失败，尝试恢复备份
		if restoreErr := sftpClient.Rename(backupPath, remotePath); restoreErr != nil {
			return fmt.Errorf("部署失败且无法恢复: %v", err)
		}
		return fmt.Errorf("部署失败，已恢复备份: %v", err)
	}

	// 删除备份目录（保留最近的备份，可选）
	// if err := sftpClient.RemoveDirectory(backupPath); err != nil {
	//     log.Warnf("删除备份目录失败: %v", err)
	// }

	return nil
}

// 连接SSH服务器
func connectSSH() (*ssh.Client, error) {
	// 配置SSH客户端
	sshConfig := &ssh.ClientConfig{
		User: config.SSH.User,
		Auth: []ssh.AuthMethod{
			ssh.Password(config.SSH.Password),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		Timeout:         10 * time.Second,
	}

	host := fmt.Sprintf("%s:%d", config.SSH.Host, config.SSH.Port)

	for attempt := 1; attempt <= config.Retry.MaxAttempts; attempt++ {
		client, err := ssh.Dial("tcp", host, sshConfig)
		if err == nil {
			return client, nil
		}

		log.WithFields(logrus.Fields{
			"attempt": attempt,
			"error":   err,
		}).Warn("SSH连接失败，准备重试")

		if attempt < config.Retry.MaxAttempts {
			time.Sleep(config.Retry.Delay)
		}
	}

	return nil, fmt.Errorf("在%d次尝试后仍无法连接SSH", config.Retry.MaxAttempts)
}

// 重启远程服务
func restartServices() error {
	log.Info("🔄 重启远程服务...")

	sshClient, err := connectSSH()
	if err != nil {
		return fmt.Errorf("SSH连接失败: %v", err)
	}
	defer sshClient.Close()

	// 重启服务的命令列表
	services := []struct {
		name     string
		commands []string
	}{
		{
			name: "API服务",
			commands: []string{
				fmt.Sprintf("cd %s && sudo systemctl restart coupon-api || sudo pkill -f api && nohup ./api > api.log 2>&1 &", config.SSH.BackendPaths.API),
			},
		},
		{
			name: "Task服务",
			commands: []string{
				fmt.Sprintf("cd %s && sudo systemctl restart coupon-task || sudo pkill -f task && nohup ./task > task.log 2>&1 &", config.SSH.BackendPaths.Task),
			},
		},
		{
			name: "Nginx",
			commands: []string{
				"sudo systemctl reload nginx || sudo nginx -s reload",
			},
		},
	}

	for _, service := range services {
		log.Infof("🔄 重启 %s...", service.name)

		for _, command := range service.commands {
			session, err := sshClient.NewSession()
			if err != nil {
				log.Warnf("创建SSH会话失败: %v", err)
				continue
			}

			output, err := session.CombinedOutput(command)
			session.Close()

			if err != nil {
				log.Warnf("%s 重启命令执行失败: %v, 输出: %s", service.name, err, string(output))
			} else {
				log.Infof("✅ %s 重启成功", service.name)
				break // 成功执行了一个命令就跳出
			}
		}
	}

	return nil
}

// 健康检查
func healthCheck() error {
	log.Info("🔍 执行健康检查...")

	// 这里可以添加各种健康检查
	// 例如：检查API是否响应、检查前端是否可访问等

	log.Info("✅ 健康检查完成")
	return nil
}

// 主函数
func main() {
	log.Info("🚀 开始 CouponsGo 完整部署流程...")
	log.Info("=" + strings.Repeat("=", 50))

	startTime := time.Now()

	// 部署步骤
	steps := []struct {
		name string
		fn   func() error
	}{
		{"🏗️  构建前端", buildFrontend},
		{"🏗️  构建后端", buildBackend},
		{"📤 上传前端文件", uploadFrontend},
		{"📤 上传后端文件", uploadBackend},
		{"🔄 重启服务", restartServices},
		{"🔍 健康检查", healthCheck},
	}

	var failedSteps []string

	for i, step := range steps {
		log.Infof("\n[%d/%d] %s", i+1, len(steps), step.name)
		log.Info(strings.Repeat("-", 40))

		stepStart := time.Now()
		if err := step.fn(); err != nil {
			log.Errorf("❌ %s 失败: %v", step.name, err)
			failedSteps = append(failedSteps, step.name)

			// 对于关键步骤失败，停止部署
			if strings.Contains(step.name, "构建") {
				log.Error("💥 构建失败，停止部署")
				os.Exit(1)
			}
		} else {
			stepDuration := time.Since(stepStart)
			log.Infof("✅ %s 完成 (耗时: %v)", step.name, stepDuration)
		}
	}

	// 部署总结
	totalDuration := time.Since(startTime)
	log.Info("\n" + strings.Repeat("=", 60))

	if len(failedSteps) == 0 {
		log.Info("🎉 部署完全成功!")
		log.Infof("⏱️  总耗时: %v", totalDuration)
		log.Info("\n🌟 部署总结:")
		log.Info("   ✅ 前端已构建并部署")
		log.Info("   ✅ 后端已构建并部署")
		log.Info("   ✅ 服务已重启")
		log.Info("   ✅ 健康检查通过")
		log.Info("\n🚀 你的 CouponsGo 网站现在可以访问了!")

		// 读取前端配置显示访问地址
		if frontendConfig, err := loadFrontendConfig(); err == nil {
			log.Infof("🌐 网站地址: %s", frontendConfig.SiteVariables.SiteURL)
		}

	} else {
		log.Errorf("⚠️  部署完成，但有 %d 个步骤失败:", len(failedSteps))
		for _, failed := range failedSteps {
			log.Errorf("   ❌ %s", failed)
		}
		log.Infof("⏱️  总耗时: %v", totalDuration)
		log.Info("\n请检查上述失败的步骤，然后重新部署")
		os.Exit(1)
	}
}
