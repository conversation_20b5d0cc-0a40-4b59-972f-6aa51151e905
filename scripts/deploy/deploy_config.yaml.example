# CouponsGo 部署配置文件

# SSH连接配置
ssh:
  host: "your-server-ip"          # 服务器IP地址
  user: "root"                    # SSH用户名
  password: "your-password"       # SSH密码 (或者使用密钥认证)
  port: 22                        # SSH端口
  frontend_path: "/var/www/coupons-go"  # 前端部署路径
  backend_paths:
    api: "/opt/coupons-go/api/"         # API服务部署路径
    migrate: "/opt/coupons-go/migrate/" # 迁移工具部署路径
    task: "/opt/coupons-go/task/"       # 任务服务部署路径

# 本地文件路径配置
local:
  frontend_dir: "../coupon-frontend"    # 前端项目目录（相对于脚本位置）
  backend_dir: "../coupon-backend"      # 后端项目目录（相对于脚本位置）
  backup_dir: "./backups"               # 本地备份目录

# 构建配置
build:
  frontend:
    build_command: "npm run build:live"  # 前端构建命令
    dist_dir: "dist"                     # 前端构建输出目录
  backend:
    build_command: "make build"          # 后端构建命令
    bin_dir: "bin"                       # 后端二进制文件输出目录
    targets:                             # 需要部署的后端服务
      - "api"
      - "migrate" 
      - "task"

# 重试配置
retry:
  max_attempts: 5        # 最大重试次数
  delay: "3s"           # 重试间隔

# 上传配置
upload:
  workers: 10           # 并发上传工作协程数
  buffer_size: 32       # 缓冲区大小(KB)

# 使用说明:
# 1. 复制此文件为 deploy_config.yaml
# 2. 修改SSH配置中的服务器信息
# 3. 确认本地项目路径配置正确
# 4. 确认远程部署路径存在
# 5. 运行: go run deploy.go