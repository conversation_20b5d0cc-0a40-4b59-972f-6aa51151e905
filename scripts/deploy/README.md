# CouponsGo 部署脚本

这是一个完整的 CouponsGo 项目构建和部署脚本，使用 Go 语言编写。

## 功能特性

### 🎯 前端构建优化
- ✨ **简化构建命令**: 只需运行 `npm run build:live`
- 📊 **预构建数据**: 自动从后端 API 获取并预构建关键数据
- 🚀 **零配置**: 从 `variables.ts` 自动读取配置，无需手动输入
- ⚡ **即时加载**: 首页和前5页数据预构建，用户体验极佳

### 🔧 完整部署流程
1. 🏗️ **自动构建**: 前端和后端自动构建
2. 📤 **并发上传**: 多线程上传，提高部署速度
3. 🔄 **服务重启**: 自动重启相关服务
4. 🔍 **健康检查**: 部署后验证系统状态
5. 📊 **进度显示**: 实时显示上传进度
6. 💾 **自动备份**: 部署前自动备份现有版本

## 安装依赖

```bash
cd scripts/deploy
go mod tidy
```

## 配置文件

复制配置模板并修改：

```bash
cp deploy_config.yaml.example deploy_config.yaml
```

编辑 `deploy_config.yaml`:

```yaml
# SSH连接配置
ssh:
  host: "your-server-ip"
  user: "root" 
  password: "your-password"
  port: 22
  frontend_path: "/var/www/coupons-go"
  backend_paths:
    api: "/opt/coupons-go/api/"
    migrate: "/opt/coupons-go/migrate/"
    task: "/opt/coupons-go/task/"

# 本地项目路径
local:
  frontend_dir: "../coupon-frontend"
  backend_dir: "../coupon-backend"
  backup_dir: "./backups"
```

## 使用方法

### 完整部署

```bash
# 使用默认配置文件
go run deploy.go

# 或指定配置文件
go run deploy.go custom_config.yaml
```

### 前端单独构建

在前端项目目录中：

```bash
npm run build:live
```

## 前端预构建策略

### 📊 预构建的数据包括：

1. **首页数据**:
   - 所有分类信息
   - 18个推荐品牌
   - 10个推荐优惠券  
   - 16个推荐优惠活动
   - 前10个分类的优惠券

2. **列表页数据**:
   - 品牌前5页数据 (每页20项)
   - 优惠券前5页数据 (每页20项) 
   - 优惠活动前5页数据 (每页20项)

3. **分类数据**:
   - 完整分类列表 (最多1000个)

### ⚡ 用户体验优化

- **首页**: 即时加载，无需API调用
- **分类页**: 即时加载所有分类
- **列表页前5页**: 即时加载，无需等待
- **第6页以后**: 实时从API获取

## 部署流程详解

### 1. 🏗️ 构建阶段
- **前端**: 运行 `npm run build:live`
  - 从 `variables.ts` 读取配置
  - 调用后端API预构建数据
  - 生成主题文件
  - 构建React应用
  - 处理HTML模板
- **后端**: 运行 `make build`
  - 编译Go二进制文件
  - 生成 api、migrate、task 三个服务

### 2. 📤 上传阶段
- **前端文件**: 并发上传整个 dist 目录
- **后端文件**: 分别上传三个服务的二进制文件
- **原子替换**: 先上传到临时位置，然后原子性替换
- **自动备份**: 替换前自动备份现有版本

### 3. 🔄 服务重启
- 重启API服务
- 重启Task服务  
- 重新加载Nginx配置

### 4. 🔍 健康检查
- 验证服务是否正常运行
- 检查API响应
- 确认网站可访问

## 日志记录

部署过程中会生成详细日志：

- **控制台输出**: 实时显示部署进度
- **日志文件**: 保存到 `deploy.log` 文件
- **结构化日志**: 使用 logrus 生成结构化日志
- **彩色输出**: 支持彩色控制台输出

## 错误处理

### 🔄 重试机制
- SSH连接失败自动重试
- 文件上传失败自动重试
- 可配置重试次数和间隔

### 💾 自动备份和恢复
- 部署前自动备份现有版本
- 部署失败自动恢复备份
- 多版本备份保留

### 🚨 失败处理
- 构建失败立即停止部署
- 上传失败回滚到备份版本
- 详细错误日志记录

## 性能优化

- **并发上传**: 多协程并发上传文件
- **内存池**: 使用内存池减少GC压力
- **进度显示**: 实时显示上传进度和速度
- **增量部署**: 只上传变更的文件（可扩展）

## 目录结构

```
scripts/deploy/
├── deploy.go                    # 主部署脚本
├── deploy_config.yaml.example  # 配置文件模板
├── go.mod                       # Go模块文件
├── README.md                    # 说明文档
└── deploy.log                   # 部署日志（运行时生成）
```

## 环境要求

### 本地环境
- Go 1.21+
- Node.js 18+
- npm/yarn

### 服务器环境  
- Linux/Unix 系统
- SSH访问权限
- 必要的目录权限
- Nginx (可选)
- systemd (可选，用于服务管理)

## 安全建议

1. **SSH密钥**: 建议使用SSH密钥而不是密码
2. **权限管理**: 使用最小必要权限
3. **配置文件**: 不要将配置文件提交到版本控制
4. **网络安全**: 确保服务器防火墙配置正确

## 故障排除

### 常见问题

1. **SSH连接失败**
   - 检查服务器IP、端口、用户名、密码
   - 确认防火墙设置
   - 检查SSH服务状态

2. **构建失败**
   - 检查Node.js和Go环境
   - 确认依赖安装完整
   - 查看详细错误日志

3. **上传失败**
   - 检查远程目录权限
   - 确认磁盘空间充足
   - 检查网络连接稳定性

4. **服务启动失败**
   - 检查二进制文件权限
   - 确认端口未被占用
   - 查看服务日志

## 扩展功能

可以根据需要扩展以下功能：

- 支持多环境部署 (dev/staging/prod)
- 集成CI/CD流水线
- 增量部署优化
- 监控和告警集成
- 数据库迁移管理
- SSL证书自动更新

---

🚀 **快速开始**: 复制配置文件，修改服务器信息，运行 `go run deploy.go` 即可开始部署！